<?php
/**
 * 易教育API连接测试脚本
 * 用于测试新的API地址是否可用
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育API连接测试</h1>\n";
echo "<pre>\n";

// 新的API地址
$api_url = "http://125.208.21.156:9900";
$username = "573749877";
$password = "liuyaxin123.";

echo "=== 易教育API连接测试 ===\n";
echo "API地址: {$api_url}\n";
echo "账号: {$username}\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 测试1：基础连接测试
echo "测试1：基础连接测试\n";
$test_url = $api_url . "/api/login";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头部信息
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

$result = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

if ($curl_error) {
    echo "❌ 连接失败: {$curl_error}\n";
    echo "可能原因：\n";
    echo "1. 网络连接问题\n";
    echo "2. 防火墙阻止\n";
    echo "3. API地址不正确\n";
    exit;
} else {
    echo "✅ 基础连接正常 (HTTP状态码: {$http_code})\n";
}

// 测试2：登录测试
echo "\n测试2：登录API测试\n";

$login_data = array(
    "username" => $username,
    "password" => $password
);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

$login_result = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
$total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
curl_close($ch);

echo "请求耗时: {$total_time}秒\n";
echo "HTTP状态码: {$http_code}\n";

if ($curl_error) {
    echo "❌ 登录请求失败: {$curl_error}\n";
    exit;
}

if ($http_code != 200) {
    echo "❌ HTTP状态码异常: {$http_code}\n";
    echo "响应内容: " . substr($login_result, 0, 500) . "\n";
    exit;
}

$login_result_array = json_decode($login_result, true);

if (!$login_result_array) {
    echo "❌ 响应解析失败\n";
    echo "原始响应: " . substr($login_result, 0, 500) . "\n";
    exit;
}

if (!isset($login_result_array["code"]) || $login_result_array["code"] != 200) {
    echo "❌ 登录失败\n";
    echo "错误信息: " . ($login_result_array["message"] ?? "未知错误") . "\n";
    echo "响应内容: " . json_encode($login_result_array, JSON_UNESCAPED_UNICODE) . "\n";
    exit;
}

echo "✅ 登录成功\n";
$token = $login_result_array["data"]["token"];
echo "Token: " . substr($token, 0, 50) . "...\n";

// 测试3：用户信息测试
echo "\n测试3：用户信息API测试\n";

$info_url = $api_url . "/api/user/info";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $info_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

$info_result = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
$total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
curl_close($ch);

echo "请求耗时: {$total_time}秒\n";

if ($curl_error) {
    echo "❌ 用户信息请求失败: {$curl_error}\n";
} else if ($http_code != 200) {
    echo "❌ HTTP状态码异常: {$http_code}\n";
} else {
    $info_result_array = json_decode($info_result, true);
    if ($info_result_array && $info_result_array["code"] == 200) {
        echo "✅ 用户信息获取成功\n";
        if (isset($info_result_array["data"]["balance"])) {
            echo "账户余额: {$info_result_array["data"]["balance"]}元\n";
        }
        if (isset($info_result_array["data"]["username"])) {
            echo "用户名: {$info_result_array["data"]["username"]}\n";
        }
    } else {
        echo "❌ 用户信息获取失败\n";
        echo "错误信息: " . ($info_result_array["message"] ?? "未知错误") . "\n";
    }
}

// 测试4：项目列表测试
echo "\n测试4：项目列表API测试\n";

$list_url = $api_url . "/api/website/list";
$list_data = array("pageSize" => 5, "pageNum" => 1); // 只获取5个项目测试

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $list_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($list_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    "Content-Type: application/json",
    "Authorization: Bearer {$token}"
));
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

$list_result = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
$total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
curl_close($ch);

echo "请求耗时: {$total_time}秒\n";

if ($curl_error) {
    echo "❌ 项目列表请求失败: {$curl_error}\n";
} else if ($http_code != 200) {
    echo "❌ HTTP状态码异常: {$http_code}\n";
} else {
    $list_result_array = json_decode($list_result, true);
    if ($list_result_array && $list_result_array["code"] == 200) {
        echo "✅ 项目列表获取成功\n";

        // 检查数据结构 - 易教育API数据直接在data数组中
        if (isset($list_result_array["data"]) && is_array($list_result_array["data"])) {
            $projects = $list_result_array["data"];
            echo "项目数量: " . count($projects) . "\n";

            if (count($projects) > 0) {
                echo "示例项目:\n";
                foreach (array_slice($projects, 0, 3) as $project) {
                    echo "- {$project['name']} (编号: {$project['number']}, 价格: {$project['price']}元)\n";
                }
            } else {
                echo "项目列表为空\n";
            }
        } else {
            echo "数据结构异常，响应内容:\n";
            echo json_encode($list_result_array, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        }
    } else {
        echo "❌ 项目列表获取失败\n";
        echo "错误信息: " . ($list_result_array["message"] ?? "未知错误") . "\n";
        if (isset($list_result_array)) {
            echo "响应内容: " . json_encode($list_result_array, JSON_UNESCAPED_UNICODE) . "\n";
        }
    }
}

// 测试总结
echo "\n=== 测试总结 ===\n";
echo "API地址: {$api_url} ✅\n";
echo "账号认证: ✅\n";
echo "Token获取: ✅\n";
echo "用户信息: " . (isset($info_result_array) && $info_result_array["code"] == 200 ? "✅" : "❌") . "\n";
echo "项目列表: " . (isset($list_result_array) && $list_result_array["code"] == 200 && isset($list_result_array["data"]) ? "✅" : "❌") . "\n";

echo "\n如果所有测试都显示 ✅，说明API连接正常，可以运行商品同步脚本。\n";
echo "商品同步地址: http://你的域名/api/jxjy.php?pricee=5\n";

echo "</pre>\n";
?>
