<?php
/**
 * 易教育进度同步测试脚本
 * 用于测试和调试易教育订单进度同步功能
 */

include('confing/common.php');
include('Checkorder/jdjk.php');

header('Content-Type: text/html; charset=utf-8');

echo "<h2>易教育进度同步测试</h2>";

// 1. 检查易教育货源配置
echo "<h3>1. 检查易教育货源配置</h3>";
$jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt='jxjy' LIMIT 1");
if ($jk) {
    echo "✅ 易教育货源配置存在<br>";
    echo "货源ID: {$jk['hid']}<br>";
    echo "货源名称: {$jk['name']}<br>";
    echo "API地址: {$jk['url']}<br>";
    echo "Token: " . (empty($jk['token']) ? "❌ 未设置" : "✅ 已设置") . "<br>";
} else {
    echo "❌ 易教育货源配置不存在<br>";
    exit;
}

// 2. 查询易教育订单
echo "<h3>2. 查询易教育订单</h3>";
$sql = "SELECT o.*, h.pt as platform_type 
        FROM qingka_wangke_order o 
        LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
        WHERE h.pt = 'jxjy' 
        ORDER BY o.addtime DESC 
        LIMIT 5";

$result = $DB->query($sql);
$orders = [];
while ($order = $DB->fetch($result)) {
    $orders[] = $order;
}

if (empty($orders)) {
    echo "❌ 没有找到易教育订单<br>";
    exit;
} else {
    echo "✅ 找到 " . count($orders) . " 个易教育订单<br>";
    foreach ($orders as $order) {
        echo "订单ID: {$order['oid']}, 用户: {$order['user']}, 状态: {$order['status']}, 进度: {$order['process']}<br>";
    }
}

// 3. 测试进度查询功能
echo "<h3>3. 测试进度查询功能</h3>";
$test_order = $orders[0]; // 使用第一个订单进行测试

echo "测试订单ID: {$test_order['oid']}<br>";
echo "测试用户: {$test_order['user']}<br>";
echo "当前状态: {$test_order['status']}<br>";
echo "当前进度: {$test_order['process']}<br>";

try {
    echo "<h4>调用 processCx 函数...</h4>";
    $progress_result = processCx($test_order['oid']);
    
    if (empty($progress_result)) {
        echo "❌ processCx 返回空结果<br>";
    } else {
        echo "✅ processCx 返回结果:<br>";
        foreach ($progress_result as $item) {
            echo "<pre>";
            print_r($item);
            echo "</pre>";
            
            if ($item['code'] == 1) {
                echo "✅ 查询成功<br>";
                echo "状态: {$item['status_text']}<br>";
                echo "进度: {$item['process']}<br>";
                echo "备注: {$item['remarks']}<br>";
                
                // 测试更新数据库
                echo "<h4>测试更新数据库...</h4>";
                $update_sql = "UPDATE qingka_wangke_order SET 
                              status = ?, 
                              process = ?, 
                              remarks = ?,
                              yid = ?,
                              uptime = NOW()
                              WHERE oid = ?";
                $update_result = $DB->prepare_query($update_sql, [
                    $item['status_text'],
                    $item['process'],
                    $item['remarks'],
                    $item['yid'],
                    $test_order['oid']
                ]);
                
                if ($update_result) {
                    echo "✅ 数据库更新成功<br>";
                } else {
                    echo "❌ 数据库更新失败<br>";
                }
            } else {
                echo "❌ 查询失败: {$item['msg']}<br>";
            }
        }
    }
} catch (Exception $e) {
    echo "❌ 异常: " . $e->getMessage() . "<br>";
}

// 4. 测试同步API
echo "<h3>4. 测试同步API</h3>";
$api_url = "http://" . $_SERVER['HTTP_HOST'] . dirname(dirname($_SERVER['REQUEST_URI'])) . "/api/jxjyapi.php?action=sync_orders";
echo "API地址: {$api_url}<br>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

$response = curl_exec($ch);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($curl_error) {
    echo "❌ cURL错误: {$curl_error}<br>";
} elseif ($http_code != 200) {
    echo "❌ HTTP错误: {$http_code}<br>";
    echo "响应内容: {$response}<br>";
} else {
    echo "✅ API调用成功<br>";
    $api_result = json_decode($response, true);
    if ($api_result) {
        echo "API返回: <pre>" . json_encode($api_result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "响应内容: {$response}<br>";
    }
}

echo "<h3>测试完成</h3>";
echo "<p>如果发现问题，请检查：</p>";
echo "<ul>";
echo "<li>易教育货源配置是否正确</li>";
echo "<li>Token是否有效</li>";
echo "<li>网络连接是否正常</li>";
echo "<li>订单数据是否完整</li>";
echo "</ul>";
?>
