<?php
include('../confing/common.php');
$act = isset($_GET["act"]) ? daddslashes($_GET["act"]) : null;
$hid = '';
$jk = $DB->get_row("select * from qingka_wangke_huoyuan where hid=$hid ");
$header = ["Content-Type: application/json; charset=utf-8", "Authorization: Bearer " . $jk['token']];
$now_time = date("Y-m-d H:i:s"); //时间
switch ($act) {
    case 'login':
        $username = $jk['user'];
        $password = $jk['pass'];
        if ($username == '') {
            // jsonReturn(-1, "账号不能为空");
            echo ("账号不能为空");
            exit;
        }
        if ($password == '') {
            // jsonReturn(-1, "密码不能为空");
            echo ("密码不能为空");
            exit;
        }
        $data = array("username" => $username, "password" => $password);
        $data = json_encode($data);
        $jxjy_url = $jk["url"] . "/api/login";
        $result = post($jxjy_url, $data, $header);
        $result = json_decode($result, true);
        if ($result['code'] == 200) {
            $DB->query("update qingka_wangke_huoyuan set token='{$result['data']['token']}' where hid='$hid}' limit 1 ");
            // jsonReturn(1, "成功登录 Token " . $result['data']['token'] . " ");
            echo "更新成功: " . $result['data']['token'] . "\r\n";
            exit;
        } else {
            echo "更新失败: " . $result['message'] . "\r\n";
            // jsonReturn(-1, $result['message']);
            exit;
        }
        break;
    case 'add':
        $data = array();
        $data = json_encode($data);
        $jxjy_url = $jk["url"] . "/api/website/list";
        $result = post($jxjy_url, $data, $header);
        $result = json_decode($result, true);
        if ($result['code'] == 200) {
            // 提取响应中的data
            $data_list = $result['data'];
            $total_count = count($data_list);
            $success_count = 0;
            $now_time = date('Y-m-d H:i:s');
            foreach ($data_list as $data_item) {
                $id = $data_item['id'];
                $number = $data_item['number'];
                $name = $data_item['name'];
                $format = $data_item['format'];
                $url = $data_item['url'];
                $price = number_format($data_item['price'] * 5, 2, '.', '');  // 价格乘以5并保留两位小数
                $unitId = $data_item['unitId'];
                $must = $data_item['must'];
                $unit = $data_item['unit'];
                $rate = $data_item['rate'];
                $isExam = $data_item['isExam'];
                $isSearchCourse = $data_item['isSearchCourse'];
                $status = $data_item['status'];
                $description = $data_item['description'];
                $remark = $data_item['remark'];
                $rs = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE id='$id' LIMIT 1");
                if (!$rs) {
                    $is = $DB->query("
                            INSERT INTO qingka_wangke_jxjyclass 
                            (id, number, name, format, url, price, unitId, must, unit, rate, isExam, isSearchCourse, status, description, remark, date) 
                            VALUES 
                            ('$id', '$number', '$name', '$format', '$url', '$price', '$unitId', '$must', '$unit', '$rate', '$isExam', '$isSearchCourse', '$status', '$description', '$remark', '$now_time')
                        ");
                    if ($is) {
                        $success_count++;
                    }
                }
            }
            echo "上架成功，共 " . $total_count . " 条记录，上架 " . $success_count . " 条记录\n";
            exit;
        } else {
            echo "上架失败: " . $result['message'] . "\r\n";
            exit;
        }
        break;

    case 'uporder':
        $i = 0;
        $a = $DB->query("select * from qingka_wangke_jxjyorder where status!='完成' and status!='异常'  and status!='已退款' and status!='已暂停' and status!='暂停中' and status!='售后完成' order by oid asc");
        foreach ($a as $b) {
            jxjyprocess($b['oid']);
            echo "-----------------------------\r\n";
            $i++;
        }
        echo "更新成功！本次更新订单共计：" . $i . "条\r\n";
        break;
}
//同步
function jxjyprocess($oid)
{
    $hid = '';
    global $DB;
    $d = $DB->get_row("select * from qingka_wangke_jxjyorder where oid='{$oid}' ");
    $jk = $DB->get_row("select * from qingka_wangke_huoyuan where hid=$hid ");
    $class = $DB->get_row("select * from qingka_wangke_jxjyclass where id='{$d["cid"]}' ");
    $isSearchCourse = $class['isSearchCourse'];
    $url = $jk["url"];
    $token = $jk["token"];
    $header = ["Content-Type: application/json; charset=utf-8", "Authorization: Bearer " . $token];
    $username = $d["user"];
    $kcname = $d["kcname"];
    $orderNumber = $d["yid"];
    $websiteNumber = $d["noun"];
    $data = array(
        "pageSize" => 10, "pageNum" => 1, "status" => "all", "websiteNumber" => $websiteNumber, "orderNumber" => $orderNumber, "username" => $username, "name" => '',
    );
    $data = json_encode($data);
    $jxjy_url = $url . "/api/order/list";
    $result = post($jxjy_url, $data, $header);
    $result = json_decode($result, true);
    if ($result['code'] === 200) {
        $list = $result['data']['list'];
        $status_mapping = [
            0 => '队列中', 1 => '完成', 2 => '运行中', 3 => '异常', 4 => '正在售后', 5 => '售后完成', 6 => '人工处理', 7 => '已退款', 8 => '暂停中', 9 => '已暂停'
        ];
        foreach ($list as $item) {
            $status_text = $status_mapping[$item['status']];

            if ($orderNumber == '' &&  $isSearchCourse == '0') { //无课程更新
                $DB->query("UPDATE qingka_wangke_jxjyorder SET `name` = '{$item['name']}', `yid` = '{$item['number']}', `status` = '{$status_text}', `process` = '{$item['progress']}' WHERE `user` = '{$item['username']}' AND `oid` = '{$oid}'");
                return ['code' => 1, 'msg' => '同步成功'];
                output($oid, $item['number'], $status_text, $item['progress']);
            } elseif ($orderNumber != '' &&  $isSearchCourse == '0') {
                $DB->query("UPDATE qingka_wangke_jxjyorder SET `name` = '{$item['name']}', `status` = '{$status_text}', `process` = '{$item['progress']}' WHERE `user` = '{$item['username']}' AND `oid` = '{$oid}'");
                output($oid, $item['number'], $status_text, $item['progress']);
                return ['code' => 1, 'msg' => '同步成功'];
            }
            $courses = [];
            foreach ($item['courses'] as $course) {

                $course_name = $course['name']; // 初始课程名称
                if (isset($course['children'])) { // 检查课程是否有子课程
                    $children_names = array_map(function ($child) {
                        return $child['name'];
                    }, $course['children']); // 获取所有子课程的名称
                    $course_name .= '----' . implode('----', $children_names); // 拼接子课程名称到主课程名称
                }
                $courses[] = [
                    'name' => $course_name,
                    'id' => $course['id']
                ];
                if ($kcname == $course_name) {
                    if ($orderNumber == '' && $isSearchCourse == '1') {
                        $DB->query("UPDATE qingka_wangke_jxjyorder SET `name` = '{$item['name']}', `yid` = '{$item['number']}', `status` = '{$status_text}', `process` = '{$item['progress']}' WHERE `user` = '{$item['username']}' AND `kcname` = '{$course_name}' AND `oid` = '{$oid}'");
                        output($oid, $item['number'], $status_text, $item['progress']);
                        return ['code' => 1, 'msg' => '同步成功'];
                    } elseif ($orderNumber != '' && $isSearchCourse == '1') {
                        $DB->query("UPDATE qingka_wangke_jxjyorder SET `name` = '{$item['name']}', `status` = '{$status_text}', `process` = '{$item['progress']}' WHERE `user` = '{$item['username']}' AND `oid` = '{$oid}'");
                        output($oid, $item['number'], $status_text, $item['progress']);
                        return ['code' => 1, 'msg' => '同步成功'];
                    } else {
                        $DB->query("UPDATE qingka_wangke_jxjyorder SET `name` = '{$item['name']}', `status` = '{$status_text}', `process` = '{$item['progress']}' WHERE `user` = '{$item['username']}' AND `kcname` = '{$course_name}' AND `oid` = '{$oid}'");
                        output($oid, $item['number'], $status_text, $item['progress']);
                        return ['code' => 1, 'msg' => '同步成功'];
                    }
                }
            }
        }
    } else {
        return ['code' => -1, 'msg' => '同步失败'];
    }
}

function output($oid, $orderNumber, $status_text, $progress)
{
    $now_time = date("Y-m-d H:i:s"); //时间
    echo "订单ID: " . $oid . "\r\n";
    echo "源站ID: " . $orderNumber . "\r\n";
    echo "状态: " . $status_text . "\r\n";
    echo "进度: " . $progress . "\r\n";
    echo "当前时间：" . $now_time . "\r\n";
}
