ps:
接口自己抓
计划自己配置
hid根据自身情况改
计划任务plan有3个和入队一样
网址/api/plan?act=xxx

前两个建议一天一次就行
同步2小时一次就行

记得上传数据库文件
然后去上架，上架访问api/plan.php?act=add会自动上架，
价格方面48行 有注释会就搞 不会就花钱找技术


所有任何都做过测试 均可正常使用
不会就花钱找技术


以下内容无需加入，just一个小功能，没啥大用，挂了计划任务用不到下面这个，
！！！需要修改接口返回内容，！！！
！！！不会就别折腾！！！
!!!实际无任何大用!!!
货源表更新按钮
<button v-if="res.hid === '' " class="btn btn-xs btn-primary" data-toggle="modal" @click="uptoken(res.hid)">更新tioken</button>

uptoken: function(hid) {
        layer.confirm('确定更新吗', {
            title: '温馨提示',
            icon: 1,
            btn: ['确定', '取消']
        }, function() {
            var load = layer.load(2);
            $.get("/api/demo.php?act=login", function(data) {
                layer.close(load);
                var response = JSON.parse(data);
                if (response.code == 1) {
                    layer.msg(response.msg, {
                        icon: 1
                    });
                } else {
                    layer.msg(response.msg, {
                        icon: 2
                    });
                }
            });
        });
    },


你觉得是狗屎就是狗屎
我也觉得写的跟狗屎一样
泛滥源码的这辈子赚不到钱