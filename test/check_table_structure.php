<?php
/**
 * 检查数据库表结构
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>检查数据库表结构</h1>\n";
echo "<pre>\n";

// 检查qingka_wangke_order表结构
echo "=== qingka_wangke_order 表结构 ===\n";
$columns = $DB->query("SHOW COLUMNS FROM qingka_wangke_order");

if ($columns) {
    printf("%-20s %-20s %-10s %-10s %-10s %-20s\n", "Field", "Type", "Null", "Key", "Default", "Extra");
    echo str_repeat("-", 100) . "\n";
    
    while ($col = $DB->fetch($columns)) {
        printf("%-20s %-20s %-10s %-10s %-10s %-20s\n", 
            $col['Field'], 
            $col['Type'], 
            $col['Null'], 
            $col['Key'], 
            $col['Default'] ?: 'NULL', 
            $col['Extra']
        );
    }
} else {
    echo "❌ 无法获取表结构\n";
}

// 检查最近的订单
echo "\n=== 最近的订单示例 ===\n";
$recent_orders = $DB->query("SELECT oid,uid,cid,hid,ptname,user,kcname,dockstatus,addtime FROM qingka_wangke_order ORDER BY oid DESC LIMIT 3");

if ($recent_orders) {
    while ($order = $DB->fetch($recent_orders)) {
        echo "订单 {$order['oid']}:\n";
        echo "  uid: {$order['uid']}\n";
        echo "  cid: {$order['cid']}\n";
        echo "  hid: {$order['hid']}\n";
        echo "  ptname: {$order['ptname']}\n";
        echo "  user: {$order['user']}\n";
        echo "  kcname: {$order['kcname']}\n";
        echo "  dockstatus: {$order['dockstatus']}\n";
        echo "  addtime: {$order['addtime']}\n";
        echo "\n";
    }
}

// 测试插入一个简单的订单
echo "=== 测试插入订单 ===\n";
$test_time = date('Y-m-d H:i:s');
$test_sql = "INSERT INTO qingka_wangke_order (uid, cid, hid, ptname, school, name, user, pass, kcid, kcname, courseEndTime, fees, noun, miaoshua, addtime, ip, dockstatus) VALUES ('1', '1', '50', '测试项目', '自动识别', '测试用户', 'test_user_" . time() . "', 'test_pass', 'test_course_" . time() . "', '测试课程', '2025-12-31', '1.00', 'test_noun', '0', '{$test_time}', '127.0.0.1', '0')";

echo "测试SQL:\n";
echo $test_sql . "\n\n";

$result = $DB->query($test_sql);
if ($result) {
    $oid = $DB->lastInsertId();
    echo "✅ 测试插入成功，订单ID: {$oid}\n";
    
    // 删除测试订单
    $DB->query("DELETE FROM qingka_wangke_order WHERE oid = '{$oid}'");
    echo "✅ 测试订单已删除\n";
} else {
    $error = $DB->error();
    echo "❌ 测试插入失败: {$error}\n";
}

echo "\n</pre>\n";
?>
