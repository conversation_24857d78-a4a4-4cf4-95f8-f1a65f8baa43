<?php
/**
 * 调试查询逻辑
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>调试查询逻辑</h1>\n";
echo "<pre>\n";

// 测试参数
$noun = '10010101';
$huoyuan_id = '50';

echo "测试查询参数:\n";
echo "  noun: {$noun}\n";
echo "  huoyuan_id: {$huoyuan_id}\n";

// 测试查询
$sql = "SELECT * FROM qingka_wangke_class WHERE noun = '{$noun}' AND docking = '{$huoyuan_id}' LIMIT 1";
echo "\nSQL查询语句:\n";
echo "  {$sql}\n";

$result = $DB->get_row($sql);
if ($result) {
    echo "\n✅ 查询成功，找到项目:\n";
    echo "  cid: {$result['cid']}\n";
    echo "  name: {$result['name']}\n";
    echo "  noun: {$result['noun']}\n";
    echo "  docking: {$result['docking']}\n";
} else {
    echo "\n❌ 查询失败，未找到匹配的项目\n";
    
    // 检查是否存在该noun的项目
    $check_noun = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE noun = '{$noun}' LIMIT 1");
    if ($check_noun) {
        echo "\n找到noun为 {$noun} 的项目:\n";
        echo "  cid: {$check_noun['cid']}\n";
        echo "  name: {$check_noun['name']}\n";
        echo "  docking: {$check_noun['docking']}\n";
        echo "  ❌ docking不匹配，期望: {$huoyuan_id}，实际: {$check_noun['docking']}\n";
    } else {
        echo "\n❌ 数据库中不存在noun为 {$noun} 的项目\n";
    }
    
    // 检查是否存在该docking的项目
    $check_docking = $DB->query("SELECT cid,name,noun,docking FROM qingka_wangke_class WHERE docking = '{$huoyuan_id}' LIMIT 3");
    echo "\n货源ID为 {$huoyuan_id} 的项目:\n";
    while ($row = $DB->fetch($check_docking)) {
        echo "  cid: {$row['cid']}, name: {$row['name']}, noun: {$row['noun']}\n";
    }
}

echo "\n</pre>\n";
?>
