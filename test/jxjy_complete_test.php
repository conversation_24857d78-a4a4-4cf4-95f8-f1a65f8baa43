<?php
/**
 * 易教育完整功能测试
 * 测试从项目列表到查课的完整流程
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');
include_once(dirname(__FILE__) . '/../Checkorder/ckjk.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育完整功能测试</h1>\n";
echo "<pre>\n";

// 获取易教育货源配置
$jxjy_huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
if (!$jxjy_huoyuan) {
    echo "❌ 未找到易教育货源配置\n";
    exit;
}

echo "✅ 易教育货源配置: ID={$jxjy_huoyuan['hid']}, 名称={$jxjy_huoyuan['name']}\n";

// 测试1：检查项目配置
echo "\n=== 测试1：检查项目配置 ===\n";
$test_project = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking='{$jxjy_huoyuan['hid']}' AND status=1 ORDER BY cid ASC LIMIT 1");
if (!$test_project) {
    echo "❌ 没有找到可用的易教育项目\n";
    exit;
}

echo "测试项目: {$test_project['name']} (ID: {$test_project['cid']})\n";
echo "  docking: {$test_project['docking']}\n";
echo "  queryplat: {$test_project['queryplat']}\n";
echo "  getnoun: {$test_project['getnoun']}\n";
echo "  noun: {$test_project['noun']}\n";

// 检查配置
$config_ok = true;
if (empty($test_project['queryplat'])) {
    echo "  ❌ queryplat字段为空\n";
    $config_ok = false;
}
if (empty($test_project['noun'])) {
    echo "  ❌ noun字段为空\n";
    $config_ok = false;
}
if ($test_project['docking'] != $jxjy_huoyuan['hid']) {
    echo "  ❌ docking字段不正确\n";
    $config_ok = false;
}

if ($config_ok) {
    echo "  ✅ 项目配置正确\n";
} else {
    echo "  ❌ 项目配置有问题，请先修复\n";
    exit;
}

// 测试2：模拟前端获取项目列表
echo "\n=== 测试2：模拟前端获取项目列表 ===\n";
// 模拟 apisub.php?act=getclassfl 的逻辑
$projects = $DB->query("SELECT cid,name,price FROM qingka_wangke_class WHERE status=1 AND docking='{$jxjy_huoyuan['hid']}' ORDER BY cid ASC LIMIT 3");
echo "前端可选择的易教育项目:\n";
while ($row = $DB->fetch($projects)) {
    echo "  - ID: {$row['cid']}, 名称: {$row['name']}, 价格: {$row['price']}\n";
}

// 测试3：模拟前端查课调用
echo "\n=== 测试3：模拟前端查课调用 ===\n";
echo "模拟用户操作:\n";
echo "1. 用户选择项目: {$test_project['name']}\n";
echo "2. 用户输入账号密码: test_user test_pass\n";
echo "3. 前端调用查课接口...\n";

// 模拟 apisub.php?act=get 的逻辑
$cid = $test_project['cid'];
$userinfo = "test_user test_pass";

echo "\n查课参数:\n";
echo "  cid: {$cid}\n";
echo "  userinfo: {$userinfo}\n";

// 获取项目信息
$rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='{$cid}' LIMIT 1");
if (!$rs) {
    echo "❌ 无法获取项目信息\n";
    exit;
}

echo "\n项目信息:\n";
echo "  queryplat: {$rs['queryplat']}\n";
echo "  getnoun: {$rs['getnoun']}\n";
echo "  noun: {$rs['noun']}\n";
echo "  docking: {$rs['docking']}\n";

// 应用修复后的逻辑
$jxjy_check = $DB->get_row("SELECT hid FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
$project_noun = ($jxjy_check && $rs['docking'] == $jxjy_check['hid']) ? $rs['noun'] : $rs['getnoun'];

echo "\n修复后的逻辑:\n";
echo "  检测到易教育项目: " . ($rs['docking'] == $jxjy_check['hid'] ? '是' : '否') . "\n";
echo "  使用字段: " . ($rs['docking'] == $jxjy_check['hid'] ? 'noun' : 'getnoun') . "\n";
echo "  项目编号: {$project_noun}\n";

// 解析用户输入
$info = explode(" ", trim($userinfo));
if (count($info) >= 2) {
    $school = "自动识别";
    $user = trim($info[0]);
    $pass = trim($info[1]);
    
    echo "\n解析的参数:\n";
    echo "  school: {$school}\n";
    echo "  user: {$user}\n";
    echo "  pass: {$pass}\n";
    
    // 测试4：调用查课函数
    echo "\n=== 测试4：调用查课函数 ===\n";
    echo "调用 getWk({$rs['queryplat']}, '{$project_noun}', '{$school}', '{$user}', '{$pass}', '{$rs['name']}')\n";
    
    try {
        $result = getWk($rs['queryplat'], $project_noun, $school, $user, $pass, $rs['name']);
        
        echo "\n查课结果:\n";
        echo "  code: " . (isset($result['code']) ? $result['code'] : 'undefined') . "\n";
        echo "  msg: " . (isset($result['msg']) ? $result['msg'] : 'undefined') . "\n";
        
        if (isset($result['data']) && is_array($result['data'])) {
            echo "  data count: " . count($result['data']) . "\n";
            if (count($result['data']) > 0) {
                echo "  first course: " . $result['data'][0]['name'] . "\n";
            }
        }
        
        if (isset($result['code'])) {
            if ($result['code'] == 0) {
                echo "  ✅ 查课成功\n";
            } elseif ($result['code'] == -1) {
                echo "  ❌ 查课失败: " . $result['msg'] . "\n";
                
                // 分析失败原因
                if (strpos($result['msg'], '项目信息不存在') !== false) {
                    echo "\n失败原因分析:\n";
                    echo "  - 可能是项目编号不正确\n";
                    echo "  - 检查noun字段是否为有效的易教育站点编号\n";
                    echo "  - 当前使用的项目编号: {$project_noun}\n";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "  ❌ 查课异常: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ 用户输入格式错误\n";
}

// 测试5：检查修复状态
echo "\n=== 测试5：检查修复状态 ===\n";
echo "修复检查清单:\n";

// 检查apisub.php修复
$apisub_content = file_get_contents(dirname(__FILE__) . '/../apisub.php');
if (strpos($apisub_content, '易教育项目特殊处理') !== false) {
    echo "  ✅ apisub.php已修复，包含易教育特殊处理逻辑\n";
} else {
    echo "  ❌ apisub.php未修复\n";
}

// 检查ckjk.php修复
$ckjk_content = file_get_contents(dirname(__FILE__) . '/../Checkorder/ckjk.php');
if (strpos($ckjk_content, 'qingka_wangke_class WHERE noun') !== false) {
    echo "  ✅ ckjk.php已修复，使用正确的数据表和字段\n";
} else {
    echo "  ❌ ckjk.php未修复\n";
}

// 检查项目配置
$bad_config_count = $DB->get_row("SELECT COUNT(*) as count FROM qingka_wangke_class WHERE docking='{$jxjy_huoyuan['hid']}' AND (queryplat = '' OR queryplat IS NULL)");
if ($bad_config_count['count'] == 0) {
    echo "  ✅ 所有易教育项目的queryplat字段已正确配置\n";
} else {
    echo "  ❌ 还有 {$bad_config_count['count']} 个易教育项目的queryplat字段未配置\n";
}

echo "\n=== 测试总结 ===\n";
echo "如果以上测试都通过，易教育查课功能应该正常工作。\n";
echo "如果前端仍然提示'项目信息不存在'，可能的原因:\n";
echo "1. 浏览器缓存问题 - 清除缓存重试\n";
echo "2. 项目编号(noun字段)在易教育系统中不存在\n";
echo "3. 易教育API服务异常\n";
echo "4. 网络连接问题\n";

echo "\n建议:\n";
echo "1. 清除浏览器缓存\n";
echo "2. 使用浏览器开发者工具查看网络请求\n";
echo "3. 检查实际发送的API请求参数\n";
echo "4. 确认易教育API服务是否正常\n";

echo "\n</pre>\n";
?>
