<?php
/**
 * 对比前端订单与测试订单的数据差异
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>对比前端订单与测试订单的数据差异</h1>\n";
echo "<pre>\n";

// 获取失败的订单ID
$failed_oid = '2147483682'; // 从错误日志中获取

echo "=== 分析失败订单: {$failed_oid} ===\n";

// 获取失败的订单信息
$failed_order = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE oid='{$failed_oid}' LIMIT 1");

if (!$failed_order) {
    echo "订单 {$failed_oid} 不存在，查找最近的失败订单...\n";
    
    // 查找最近的易教育订单
    $recent_orders = $DB->query("SELECT * FROM qingka_wangke_order WHERE hid=50 ORDER BY oid DESC LIMIT 10");
    
    echo "最近的易教育订单:\n";
    while ($row = $DB->fetch($recent_orders)) {
        echo "订单 {$row['oid']}: cid={$row['cid']}, noun='{$row['noun']}', dockstatus={$row['dockstatus']}, user={$row['user']}\n";
        
        if ($row['dockstatus'] == 0 || $row['dockstatus'] == 5) {
            $failed_order = $row;
            echo "  → 使用此订单进行分析\n";
            break;
        }
    }
    
    if (!$failed_order) {
        echo "❌ 没有找到可分析的订单\n";
        exit;
    }
}

echo "\n前端创建的订单信息:\n";
foreach ($failed_order as $key => $value) {
    echo "  {$key}: '{$value}'\n";
}

// 检查关键字段
echo "\n=== 关键字段检查 ===\n";
$cid = $failed_order['cid'];
$hid = $failed_order['hid'];
$noun = $failed_order['noun'];

echo "关键字段值:\n";
echo "  cid: '{$cid}'\n";
echo "  hid: '{$hid}'\n";
echo "  noun: '{$noun}'\n";

// 检查cid是否存在于商品表
echo "\n检查商品表中的cid:\n";
$class_info = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = '{$cid}' LIMIT 1");

if ($class_info) {
    echo "✅ 商品表中存在cid={$cid}\n";
    echo "  商品名称: {$class_info['name']}\n";
    echo "  商品docking: {$class_info['docking']}\n";
    echo "  商品noun: '{$class_info['noun']}'\n";
    echo "  商品status: {$class_info['status']}\n";
    
    if ($class_info['docking'] == $hid) {
        echo "✅ 商品属于正确的货源\n";
    } else {
        echo "❌ 商品不属于期望的货源\n";
        echo "  期望货源: {$hid}\n";
        echo "  实际货源: {$class_info['docking']}\n";
    }
    
    if ($class_info['status'] == 1) {
        echo "✅ 商品状态正常\n";
    } else {
        echo "❌ 商品状态异常: {$class_info['status']}\n";
    }
    
} else {
    echo "❌ 商品表中不存在cid={$cid}\n";
    
    // 检查是否有类似的商品
    echo "\n查找类似的商品:\n";
    $similar_products = $DB->query("SELECT cid,name,docking,noun,status FROM qingka_wangke_class WHERE docking=50 LIMIT 5");
    while ($product = $DB->fetch($similar_products)) {
        echo "  cid={$product['cid']}: {$product['name']} (noun='{$product['noun']}', status={$product['status']})\n";
    }
}

// 检查货源信息
echo "\n检查货源信息:\n";
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$hid}' LIMIT 1");

if ($huoyuan) {
    echo "✅ 货源信息存在\n";
    echo "  货源名称: {$huoyuan['name']}\n";
    echo "  货源类型: {$huoyuan['pt']}\n";
    echo "  API地址: {$huoyuan['url']}\n";
} else {
    echo "❌ 货源信息不存在，hid={$hid}\n";
}

// 模拟addWk函数的执行过程
echo "\n=== 模拟addWk函数执行 ===\n";

echo "步骤1: 获取订单信息\n";
echo "  oid: {$failed_order['oid']}\n";
echo "  cid: {$failed_order['cid']}\n";
echo "  noun: '{$failed_order['noun']}'\n";

echo "\n步骤2: 获取商品信息\n";
echo "  查询: SELECT * FROM qingka_wangke_class WHERE cid='{$cid}'\n";
$b = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='{$cid}' LIMIT 1");
if ($b) {
    echo "  ✅ 商品信息获取成功\n";
    echo "  docking: {$b['docking']}\n";
} else {
    echo "  ❌ 商品信息获取失败\n";
    exit;
}

echo "\n步骤3: 获取货源信息\n";
$hid_from_class = $b['docking'];
echo "  查询: SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$hid_from_class}'\n";
$a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$hid_from_class}' LIMIT 1");
if ($a) {
    echo "  ✅ 货源信息获取成功\n";
    echo "  货源类型: {$a['pt']}\n";
} else {
    echo "  ❌ 货源信息获取失败\n";
    exit;
}

echo "\n步骤4: 检查是否为易教育\n";
if ($a['pt'] == 'jxjy') {
    echo "  ✅ 确认为易教育订单\n";
} else {
    echo "  ❌ 不是易教育订单，类型: {$a['pt']}\n";
    exit;
}

echo "\n步骤5: 项目信息查询（修复后的逻辑）\n";
echo "  查询: SELECT * FROM qingka_wangke_class WHERE cid = '{$cid}'\n";
$class_info_new = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = '{$cid}' LIMIT 1");

if ($class_info_new) {
    echo "  ✅ 项目信息查询成功\n";
    echo "  项目名称: {$class_info_new['name']}\n";
    echo "  项目noun: '{$class_info_new['noun']}'\n";
    
    if ($class_info_new['docking'] == $hid_from_class) {
        echo "  ✅ 项目货源验证通过\n";
    } else {
        echo "  ❌ 项目货源验证失败\n";
        echo "    期望: {$hid_from_class}\n";
        echo "    实际: {$class_info_new['docking']}\n";
    }
} else {
    echo "  ❌ 项目信息查询失败\n";
    echo "  这就是问题所在！\n";
}

// 检查前端下单的流程
echo "\n=== 检查前端下单流程 ===\n";

// 检查是否是通过易教育API下单
if (strpos($failed_order['ptname'], '易教育') !== false || $failed_order['hid'] == 50) {
    echo "订单来源: 易教育API下单\n";
    
    // 检查api/jxjyapi.php的下单逻辑
    echo "检查 api/jxjyapi.php 的下单逻辑...\n";
    
} else {
    echo "订单来源: 主系统下单 (apisub.php)\n";
    
    // 检查apisub.php的下单逻辑
    echo "检查 apisub.php 的下单逻辑...\n";
}

echo "\n=== 问题诊断 ===\n";

if (!$class_info_new) {
    echo "❌ 问题确认: 前端下单创建的订单中的cid在商品表中不存在\n";
    echo "\n可能的原因:\n";
    echo "1. 前端传递了错误的cid\n";
    echo "2. 商品被删除或状态异常\n";
    echo "3. 数据库数据不一致\n";
    
    echo "\n解决方案:\n";
    echo "1. 检查前端下单时的cid来源\n";
    echo "2. 确保商品表数据完整\n";
    echo "3. 添加数据验证逻辑\n";
    
} else {
    echo "✅ 数据结构正常，问题可能在其他地方\n";
}

echo "\n</pre>\n";
?>
