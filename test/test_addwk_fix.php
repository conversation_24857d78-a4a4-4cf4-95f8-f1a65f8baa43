<?php
/**
 * 测试addWk函数修复效果
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');
include_once(dirname(__FILE__) . '/../Checkorder/xdjk.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>测试addWk函数修复效果</h1>\n";
echo "<pre>\n";

// 查找一个易教育订单进行测试
echo "=== 查找测试订单 ===\n";
$test_order = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE hid=50 AND dockstatus=0 ORDER BY oid DESC LIMIT 1");

if (!$test_order) {
    echo "没有找到dockstatus=0的易教育订单，创建一个测试订单...\n";
    
    // 获取一个易教育项目
    $jxjy_project = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking=50 AND status=1 LIMIT 1");
    if (!$jxjy_project) {
        echo "❌ 没有找到易教育项目\n";
        exit;
    }
    
    // 创建测试订单
    $current_time = date('Y-m-d H:i:s');
    $test_user = 'test_user_' . time();
    $test_course = 'test_course_' . time();
    
    $insert_sql = "INSERT INTO qingka_wangke_order (uid, cid, hid, ptname, school, name, user, pass, kcid, kcname, courseEndTime, fees, noun, miaoshua, addtime, ip, dockstatus, status, process) VALUES ('1', '{$jxjy_project['cid']}', '50', '{$jxjy_project['name']}', '自动识别', '测试用户', '{$test_user}', 'test_pass', '{$test_course}', '测试课程', '2025-12-31', '1.00', '{$jxjy_project['noun']}', '0', '{$current_time}', '127.0.0.1', '0', '已提交', '0%')";
    
    if ($DB->query($insert_sql)) {
        $test_oid = $DB->lastInsertId();
        $test_order = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE oid='{$test_oid}' LIMIT 1");
        echo "✅ 创建测试订单成功: {$test_oid}\n";
    } else {
        echo "❌ 创建测试订单失败: " . $DB->error() . "\n";
        exit;
    }
}

echo "\n测试订单信息:\n";
echo "  oid: {$test_order['oid']}\n";
echo "  cid: {$test_order['cid']}\n";
echo "  hid: {$test_order['hid']}\n";
echo "  noun: '{$test_order['noun']}'\n";
echo "  user: {$test_order['user']}\n";
echo "  kcname: {$test_order['kcname']}\n";
echo "  dockstatus: {$test_order['dockstatus']}\n";

// 测试addWk函数
echo "\n=== 测试addWk函数 ===\n";
echo "调用 addWk({$test_order['oid']})\n";

try {
    $result = addWk($test_order['oid']);
    
    echo "\n提交结果:\n";
    echo "  code: " . (isset($result['code']) ? $result['code'] : 'undefined') . "\n";
    echo "  msg: " . (isset($result['msg']) ? $result['msg'] : 'undefined') . "\n";
    
    if (isset($result['yid'])) {
        echo "  yid: " . $result['yid'] . "\n";
    }
    
    if (isset($result['code'])) {
        if ($result['code'] == 1) {
            echo "✅ 订单提交成功\n";
            
            // 更新订单状态
            $yid = isset($result['yid']) ? $result['yid'] : 'test_yid_' . time();
            $update_sql = "UPDATE qingka_wangke_order SET status='上号中', dockstatus=1, yid='{$yid}' WHERE oid='{$test_order['oid']}'";
            if ($DB->query($update_sql)) {
                echo "✅ 订单状态更新成功\n";
            } else {
                echo "❌ 订单状态更新失败\n";
            }
            
        } else {
            echo "❌ 订单提交失败: " . $result['msg'] . "\n";
            
            // 分析失败原因
            if (strpos($result['msg'], '项目信息不存在') !== false) {
                echo "\n失败原因分析:\n";
                echo "  - 仍然是项目信息查询问题\n";
                echo "  - 需要进一步检查查询逻辑\n";
            } elseif (strpos($result['msg'], 'Token') !== false) {
                echo "\n失败原因分析:\n";
                echo "  - Token相关问题\n";
                echo "  - 检查易教育货源的Token配置\n";
            } elseif (strpos($result['msg'], '网络') !== false) {
                echo "\n失败原因分析:\n";
                echo "  - 网络连接问题\n";
                echo "  - 检查易教育API是否可访问\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ addWk函数调用异常: " . $e->getMessage() . "\n";
}

// 验证修复逻辑
echo "\n=== 验证修复逻辑 ===\n";
$cid = $test_order['cid'];
$hid = $test_order['hid'];

echo "验证项目查询逻辑:\n";
echo "  使用cid查询: SELECT * FROM qingka_wangke_class WHERE cid = '{$cid}'\n";

$class_info = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = '{$cid}' LIMIT 1");
if ($class_info) {
    echo "  ✅ 能够找到项目信息\n";
    echo "    项目名称: {$class_info['name']}\n";
    echo "    项目docking: {$class_info['docking']}\n";
    echo "    项目noun: '{$class_info['noun']}'\n";
    
    if ($class_info['docking'] == $hid) {
        echo "  ✅ 项目属于正确的货源\n";
    } else {
        echo "  ❌ 项目不属于期望的货源\n";
        echo "    期望货源: {$hid}\n";
        echo "    实际货源: {$class_info['docking']}\n";
    }
} else {
    echo "  ❌ 无法找到项目信息\n";
}

// 清理测试订单（如果是新创建的）
if (isset($test_oid)) {
    echo "\n清理测试订单...\n";
    $DB->query("DELETE FROM qingka_wangke_order WHERE oid = '{$test_oid}'");
    echo "✅ 测试订单已删除\n";
}

echo "\n=== 测试总结 ===\n";
echo "修复内容:\n";
echo "1. ✅ 将项目查询从使用noun字段改为使用cid字段\n";
echo "2. ✅ 添加了项目货源验证逻辑\n";
echo "3. ✅ 改进了错误信息的详细程度\n";

echo "\n预期效果:\n";
echo "- addWk函数应该能正确找到项目信息\n";
echo "- 不再出现'项目信息不存在'错误\n";
echo "- 订单能正常提交到易教育上游\n";

echo "\n</pre>\n";
?>
