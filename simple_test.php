<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "简单测试易教育进度查询...\n";

// 包含必要文件
include('confing/common.php');

// 手动定义 get_url 函数
function get_url($url, $post = false, $cookie = false, $header = false) {
    $ch = curl_init();
    if ($header) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    } else {
        curl_setopt($ch, CURLOPT_HEADER, 0);
    }
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.62 Safari/537.36");
    if ($post) {
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post));
    }
    if ($cookie) {
        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
    }
    $result = curl_exec($ch);
    curl_close($ch);
    return $result;
}

// 包含进度查询函数
include('Checkorder/jdjk.php');

// 查询易教育订单
$sql = "SELECT o.oid, o.user, o.status, o.process, h.pt 
        FROM qingka_wangke_order o 
        LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
        WHERE h.pt = 'jxjy' 
        ORDER BY o.addtime DESC 
        LIMIT 1";

$result = $DB->query($sql);
if ($result) {
    $order = $DB->fetch($result);
    if ($order) {
        echo "找到易教育订单: {$order['oid']}\n";
        echo "当前状态: {$order['status']}\n";
        echo "当前进度: {$order['process']}\n";
        
        echo "开始测试进度查询...\n";
        try {
            $progress_result = processCx($order['oid']);
            
            echo "查询结果:\n";
            if (is_array($progress_result)) {
                foreach ($progress_result as $i => $item) {
                    echo "结果 $i:\n";
                    if (is_array($item)) {
                        foreach ($item as $key => $value) {
                            echo "  $key: $value\n";
                        }
                    } else {
                        echo "  非数组项: $item\n";
                    }
                }
            } else {
                echo "非数组结果: ";
                var_dump($progress_result);
            }
            
        } catch (Exception $e) {
            echo "异常: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "没有找到易教育订单\n";
    }
} else {
    echo "数据库查询失败\n";
}

echo "测试完成\n";
?>
