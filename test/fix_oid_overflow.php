<?php
/**
 * 修复oid字段溢出问题
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>修复oid字段溢出问题</h1>\n";
echo "<pre>\n";

// 检查当前的AUTO_INCREMENT值
echo "=== 检查当前AUTO_INCREMENT值 ===\n";
$table_status = $DB->query("SHOW TABLE STATUS LIKE 'qingka_wangke_order'");
if ($table_status) {
    $status = $DB->fetch($table_status);
    echo "当前AUTO_INCREMENT值: {$status['Auto_increment']}\n";
    echo "最大值限制: 2147483647 (INT类型)\n";
    
    if ($status['Auto_increment'] >= 2147483647) {
        echo "❌ AUTO_INCREMENT值已达到上限！\n";
    } else {
        echo "✅ AUTO_INCREMENT值正常\n";
    }
}

// 检查oid字段类型
echo "\n=== 检查oid字段类型 ===\n";
$columns = $DB->query("SHOW COLUMNS FROM qingka_wangke_order WHERE Field='oid'");
if ($columns) {
    $col = $DB->fetch($columns);
    echo "字段名: {$col['Field']}\n";
    echo "字段类型: {$col['Type']}\n";
    echo "是否为主键: " . ($col['Key'] == 'PRI' ? '是' : '否') . "\n";
    echo "额外属性: {$col['Extra']}\n";
    
    if (strpos($col['Type'], 'int(') !== false && strpos($col['Extra'], 'auto_increment') !== false) {
        echo "⚠️  字段类型为INT，可能会溢出\n";
    }
}

// 检查最大的oid值
echo "\n=== 检查最大oid值 ===\n";
$max_oid = $DB->get_row("SELECT MAX(oid) as max_oid FROM qingka_wangke_order");
if ($max_oid) {
    echo "当前最大oid: {$max_oid['max_oid']}\n";
    
    if ($max_oid['max_oid'] >= 2147483647) {
        echo "❌ 最大oid已达到INT类型上限\n";
    }
}

// 解决方案
echo "\n=== 解决方案 ===\n";
echo "问题: oid字段(INT类型)的AUTO_INCREMENT值达到最大值2147483647\n";
echo "\n可选解决方案:\n";
echo "1. 将oid字段类型改为BIGINT (推荐)\n";
echo "2. 重置AUTO_INCREMENT值 (风险较高)\n";
echo "3. 清理旧订单数据 (需要谨慎操作)\n";

// 方案1: 修改字段类型为BIGINT
echo "\n=== 方案1: 修改字段类型为BIGINT ===\n";
if (isset($_GET['fix']) && $_GET['fix'] == 'bigint') {
    echo "正在执行修改...\n";
    
    // 备份当前表结构
    echo "1. 备份表结构...\n";
    $backup_sql = "CREATE TABLE qingka_wangke_order_backup_" . date('Ymd_His') . " LIKE qingka_wangke_order";
    if ($DB->query($backup_sql)) {
        echo "✅ 表结构备份成功\n";
    } else {
        echo "❌ 表结构备份失败\n";
        exit;
    }
    
    // 修改oid字段类型
    echo "2. 修改oid字段类型为BIGINT...\n";
    $alter_sql = "ALTER TABLE qingka_wangke_order MODIFY COLUMN oid BIGINT NOT NULL AUTO_INCREMENT";
    if ($DB->query($alter_sql)) {
        echo "✅ 字段类型修改成功\n";
        
        // 重置AUTO_INCREMENT值
        echo "3. 重置AUTO_INCREMENT值...\n";
        $reset_sql = "ALTER TABLE qingka_wangke_order AUTO_INCREMENT = 1";
        if ($DB->query($reset_sql)) {
            echo "✅ AUTO_INCREMENT值重置成功\n";
        } else {
            echo "❌ AUTO_INCREMENT值重置失败\n";
        }
        
    } else {
        echo "❌ 字段类型修改失败: " . $DB->error() . "\n";
    }
    
} else {
    echo "SQL命令:\n";
    echo "ALTER TABLE qingka_wangke_order MODIFY COLUMN oid BIGINT NOT NULL AUTO_INCREMENT;\n";
    echo "ALTER TABLE qingka_wangke_order AUTO_INCREMENT = 1;\n";
    echo "\n如果要自动执行，请访问: " . $_SERVER['REQUEST_URI'] . "?fix=bigint\n";
}

// 方案2: 重置AUTO_INCREMENT值（风险较高）
echo "\n=== 方案2: 重置AUTO_INCREMENT值 ===\n";
if (isset($_GET['fix']) && $_GET['fix'] == 'reset') {
    echo "正在执行重置...\n";
    
    // 找一个安全的起始值
    $safe_start = $max_oid['max_oid'] + 1000;
    if ($safe_start > 2147483647) {
        echo "❌ 无法找到安全的起始值，建议使用方案1\n";
    } else {
        $reset_sql = "ALTER TABLE qingka_wangke_order AUTO_INCREMENT = {$safe_start}";
        if ($DB->query($reset_sql)) {
            echo "✅ AUTO_INCREMENT值重置为: {$safe_start}\n";
        } else {
            echo "❌ AUTO_INCREMENT值重置失败: " . $DB->error() . "\n";
        }
    }
    
} else {
    echo "⚠️  风险较高，不推荐使用\n";
    echo "如果要执行，请访问: " . $_SERVER['REQUEST_URI'] . "?fix=reset\n";
}

// 测试插入
echo "\n=== 测试插入 ===\n";
if (isset($_GET['test']) && $_GET['test'] == '1') {
    echo "测试插入新订单...\n";
    
    $test_time = date('Y-m-d H:i:s');
    $test_sql = "INSERT INTO qingka_wangke_order (uid, cid, hid, ptname, school, name, user, pass, kcid, kcname, courseEndTime, fees, noun, miaoshua, addtime, ip, dockstatus) VALUES ('1', '1', '50', '测试项目', '自动识别', '测试用户', 'test_user_" . time() . "', 'test_pass', 'test_course_" . time() . "', '测试课程', '2025-12-31', '1.00', 'test_noun', '0', '{$test_time}', '127.0.0.1', '0')";
    
    $result = $DB->query($test_sql);
    if ($result) {
        $oid = $DB->lastInsertId();
        echo "✅ 测试插入成功，新订单ID: {$oid}\n";
        
        // 删除测试订单
        $DB->query("DELETE FROM qingka_wangke_order WHERE oid = '{$oid}'");
        echo "✅ 测试订单已删除\n";
    } else {
        $error = $DB->error();
        echo "❌ 测试插入失败: {$error}\n";
    }
} else {
    echo "如果要测试插入，请访问: " . $_SERVER['REQUEST_URI'] . "?test=1\n";
}

echo "\n=== 建议 ===\n";
echo "1. 推荐使用方案1（修改为BIGINT类型），这是最安全的解决方案\n";
echo "2. BIGINT类型可以支持到 9,223,372,036,854,775,807，基本不会再溢出\n";
echo "3. 修改后需要重新测试前端提交功能\n";
echo "4. 建议在低峰期进行修改操作\n";

echo "\n</pre>\n";
?>
