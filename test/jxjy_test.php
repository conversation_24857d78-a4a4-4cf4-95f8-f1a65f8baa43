<?php
/**
 * 易教育对接功能测试脚本
 * 用于验证易教育对接的各项功能是否正常
 */

// 引入公共配置文件
include('../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育对接功能测试</h1>\n";
echo "<pre>\n";

// 测试1：检查货源配置
echo "=== 测试1：检查货源配置 ===\n";
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' LIMIT 1");
if ($huoyuan) {
    echo "✅ 货源配置正常\n";
    echo "   货源ID: {$huoyuan['hid']}\n";
    echo "   货源名称: {$huoyuan['name']}\n";
    echo "   API地址: {$huoyuan['url']}\n";
    echo "   账号: {$huoyuan['user']}\n";
    echo "   状态: " . ($huoyuan['status'] == 1 ? '启用' : '禁用') . "\n";
} else {
    echo "❌ 货源配置不存在，请先添加易教育货源\n";
    exit;
}

// 测试2：检查分类配置
echo "\n=== 测试2：检查分类配置 ===\n";
$fenlei = $DB->get_row("SELECT * FROM qingka_wangke_fenlei WHERE name = '易教育' LIMIT 1");
if ($fenlei) {
    echo "✅ 分类配置正常\n";
    echo "   分类ID: {$fenlei['id']}\n";
    echo "   分类名称: {$fenlei['name']}\n";
    echo "   状态: " . ($fenlei['status'] == 1 ? '启用' : '禁用') . "\n";
} else {
    echo "❌ 分类配置不存在，请先添加易教育分类\n";
    exit;
}

// 测试3：检查数据库表
echo "\n=== 测试3：检查数据库表 ===\n";
$tables = array('qingka_wangke_jxjyclass', 'qingka_wangke_jxjyorder');
foreach ($tables as $table) {
    $result = $DB->query("SHOW TABLES LIKE '{$table}'");
    if ($result && $DB->num_rows($result) > 0) {
        echo "✅ 表 {$table} 存在\n";
        
        // 检查表结构
        $count = $DB->get_var("SELECT COUNT(*) FROM {$table}");
        echo "   记录数: {$count}\n";
    } else {
        echo "❌ 表 {$table} 不存在，请执行SQL脚本创建\n";
    }
}

// 测试4：测试API连接
echo "\n=== 测试4：测试API连接 ===\n";
$login_data = array(
    "username" => $huoyuan["user"],
    "password" => $huoyuan["pass"]
);

$login_url = "{$huoyuan["url"]}/api/login";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $login_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
$login_result = curl_exec($ch);
$curl_error = curl_error($ch);
curl_close($ch);

if ($curl_error) {
    echo "❌ API连接失败：{$curl_error}\n";
} else {
    $login_result_array = json_decode($login_result, true);
    if ($login_result_array && $login_result_array['code'] == 200) {
        echo "✅ API连接正常\n";
        echo "   登录成功，Token已获取\n";
        
        $token = $login_result_array['data']['token'];
        
        // 测试用户信息接口
        $info_url = "{$huoyuan["url"]}/api/user/info";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $info_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
        $info_result = curl_exec($ch);
        curl_close($ch);
        
        $info_result_array = json_decode($info_result, true);
        if ($info_result_array && $info_result_array['code'] == 200) {
            echo "   用户信息获取成功\n";
            if (isset($info_result_array['data']['balance'])) {
                echo "   账户余额: {$info_result_array['data']['balance']}元\n";
            }
        }
        
        // 更新数据库中的token
        $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$huoyuan['hid']}'");
        
    } else {
        echo "❌ API登录失败：" . ($login_result_array['message'] ?? '未知错误') . "\n";
    }
}

// 测试5：检查商品数据
echo "\n=== 测试5：检查商品数据 ===\n";
$class_count = $DB->get_var("SELECT COUNT(*) FROM qingka_wangke_class WHERE docking = '{$huoyuan['hid']}'");
echo "易教育商品数量: {$class_count}\n";

if ($class_count > 0) {
    echo "✅ 商品数据正常\n";
    
    // 显示几个示例商品
    $sample_classes = $DB->get_rows("SELECT name, price, noun FROM qingka_wangke_class WHERE docking = '{$huoyuan['hid']}' AND status = 1 LIMIT 3");
    echo "   示例商品:\n";
    foreach ($sample_classes as $class) {
        echo "   - {$class['name']} (编号: {$class['noun']}, 价格: {$class['price']}元)\n";
    }
} else {
    echo "⚠️  商品数据为空，请运行同步脚本：你的域名/api/jxjy.php?pricee=5\n";
}

// 测试6：检查易教育专用表数据
echo "\n=== 测试6：检查易教育专用表数据 ===\n";
$jxjy_class_count = $DB->get_var("SELECT COUNT(*) FROM qingka_wangke_jxjyclass WHERE status = 1");
echo "易教育项目数量: {$jxjy_class_count}\n";

if ($jxjy_class_count > 0) {
    echo "✅ 易教育项目数据正常\n";
    
    // 显示项目统计
    $must_count = $DB->get_var("SELECT COUNT(*) FROM qingka_wangke_jxjyclass WHERE must = '1' AND status = 1");
    $no_must_count = $DB->get_var("SELECT COUNT(*) FROM qingka_wangke_jxjyclass WHERE must = '0' AND status = 1");
    echo "   需要查课项目: {$must_count}\n";
    echo "   无需查课项目: {$no_must_count}\n";
    
    // 显示价格范围
    $min_price = $DB->get_var("SELECT MIN(price) FROM qingka_wangke_jxjyclass WHERE status = 1");
    $max_price = $DB->get_var("SELECT MAX(price) FROM qingka_wangke_jxjyclass WHERE status = 1");
    echo "   价格范围: {$min_price}元 - {$max_price}元\n";
} else {
    echo "⚠️  易教育项目数据为空，请运行同步脚本\n";
}

// 测试7：检查订单表
echo "\n=== 测试7：检查订单表 ===\n";
$order_count = $DB->get_var("SELECT COUNT(*) FROM qingka_wangke_jxjyorder");
echo "易教育订单数量: {$order_count}\n";

if ($order_count > 0) {
    echo "✅ 订单数据存在\n";
    
    // 显示订单状态统计
    $status_stats = $DB->get_rows("SELECT status, COUNT(*) as count FROM qingka_wangke_jxjyorder GROUP BY status");
    echo "   订单状态统计:\n";
    foreach ($status_stats as $stat) {
        echo "   - {$stat['status']}: {$stat['count']}个\n";
    }
} else {
    echo "ℹ️  暂无易教育订单\n";
}

// 测试8：检查接口文件
echo "\n=== 测试8：检查接口文件 ===\n";
$interface_files = array(
    'Checkorder/ckjk.php' => '查课接口',
    'Checkorder/xdjk.php' => '下单接口', 
    'Checkorder/jdjk.php' => '进度查询接口',
    'Checkorder/bsjk.php' => '补刷接口',
    'Checkorder/xgjk.php' => '修改密码接口',
    'api/jxjy.php' => '商品同步脚本',
    'api/jxjyapi.php' => 'API管理脚本',
    'api/plan.php' => '计划任务脚本'
);

foreach ($interface_files as $file => $desc) {
    if (file_exists("../{$file}")) {
        echo "✅ {$desc} ({$file}) 存在\n";
        
        // 检查文件中是否包含易教育相关代码
        $content = file_get_contents("../{$file}");
        if (strpos($content, 'jxjy') !== false || strpos($content, '易教育') !== false) {
            echo "   包含易教育相关代码\n";
        } else {
            echo "   ⚠️  未找到易教育相关代码\n";
        }
    } else {
        echo "❌ {$desc} ({$file}) 不存在\n";
    }
}

// 测试总结
echo "\n=== 测试总结 ===\n";
echo "易教育对接功能测试完成！\n";
echo "\n如果所有测试项都显示 ✅，说明易教育对接功能正常。\n";
echo "如果有 ❌ 或 ⚠️  项目，请按照提示进行修复。\n";
echo "\n下一步操作：\n";
echo "1. 运行商品同步：你的域名/api/jxjy.php?pricee=5\n";
echo "2. 设置计划任务定期同步商品和订单\n";
echo "3. 在前台测试查课和下单功能\n";

echo "</pre>\n";
?>
