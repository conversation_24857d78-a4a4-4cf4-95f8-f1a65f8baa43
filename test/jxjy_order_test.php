<?php
/**
 * 易教育下单功能测试脚本
 * 用于诊断下单接口的问题
 */

// 引入公共配置文件
include('../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育下单功能测试</h1>\n";
echo "<pre>\n";

echo "=== 易教育下单测试 ===\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n\n";

// 步骤1：获取易教育配置
echo "步骤1：获取易教育配置\n";
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' LIMIT 1");
if (!$huoyuan) {
    die("❌ 未找到易教育货源配置\n");
}

echo "✅ 货源配置正常\n";
echo "   API地址: {$huoyuan['url']}\n";
echo "   账号: {$huoyuan['user']}\n";

// 步骤2：获取Token
echo "\n步骤2：获取访问Token\n";
$token = $huoyuan['token'];

if (empty($token)) {
    echo "Token为空，重新登录...\n";
    
    $login_data = array(
        "username" => $huoyuan["user"],
        "password" => $huoyuan["pass"]
    );

    $login_url = "{$huoyuan["url"]}/api/login";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $login_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $login_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($curl_error || $http_code != 200) {
        die("❌ 登录失败: {$curl_error} (HTTP: {$http_code})\n");
    }

    $login_result_array = json_decode($login_result, true);
    if (!$login_result_array || $login_result_array["code"] != 200) {
        die("❌ 登录失败: " . ($login_result_array["message"] ?? "未知错误") . "\n");
    }

    $token = $login_result_array["data"]["token"];
    echo "✅ 重新登录成功\n";
} else {
    echo "✅ 使用缓存Token\n";
}

echo "Token: " . substr($token, 0, 30) . "...\n";

// 步骤3：获取可用项目
echo "\n步骤3：获取可用项目\n";

// 先检查表是否存在
$table_check = $DB->query("SHOW TABLES LIKE 'qingka_wangke_jxjyclass'");
if (!$table_check || $DB->num_rows($table_check) == 0) {
    die("❌ 易教育项目表不存在，请先运行数据库创建脚本\n");
}

// 获取项目数据
$projects = array();
$projects_result = $DB->query("SELECT * FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 3");
if ($projects_result && $DB->num_rows($projects_result) > 0) {
    while ($row = $DB->fetch_array($projects_result)) {
        $projects[] = $row;
    }
}

if (empty($projects)) {
    die("❌ 未找到可用的易教育项目\n");
}

echo "✅ 找到 " . count($projects) . " 个可用项目\n";

// 选择第一个项目进行测试
$test_project = $projects[0];
echo "测试项目: {$test_project['name']}\n";
echo "项目编号: {$test_project['number']}\n";
echo "是否需要查课: " . ($test_project['isSearchCourse'] == '1' ? '是' : '否') . "\n";

// 步骤4：测试查课功能（如果需要）
if ($test_project['isSearchCourse'] == '1') {
    echo "\n步骤4：测试查课功能\n";
    
    // 使用测试账号密码
    $test_user = "testuser123";
    $test_pass = "testpass123";
    
    echo "测试账号: {$test_user}\n";
    echo "测试密码: {$test_pass}\n";
    
    $query_data = array(
        "websiteNumber" => $test_project['number'],
        "data" => array(array("username" => $test_user, "password" => $test_pass))
    );

    $query_url = "{$huoyuan["url"]}/api/website/queryCourse";
    
    echo "查课URL: {$query_url}\n";
    echo "查课数据: " . json_encode($query_data, JSON_UNESCAPED_UNICODE) . "\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $query_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($query_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        "Content-Type: application/json",
        "Authorization: Bearer {$token}"
    ));
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $query_result = curl_exec($ch);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "查课HTTP状态码: {$http_code}\n";

    if ($curl_error) {
        echo "❌ 查课请求失败: {$curl_error}\n";
    } else {
        $query_result_array = json_decode($query_result, true);
        if ($query_result_array && $query_result_array["code"] == 200) {
            echo "✅ 查课请求成功\n";
            $uuid = $query_result_array['data']['uuid'];
            echo "查课UUID: {$uuid}\n";
            
            // 等待并获取查课结果
            echo "等待查课结果...\n";
            sleep(3);
            
            $result_data = array("uuid" => $uuid);
            $result_url = "{$huoyuan["url"]}/api/website/getQueryCourse";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $result_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($result_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization: Bearer {$token}"
            ));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            $result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if (!$curl_error) {
                $result_array = json_decode($result, true);
                if ($result_array && $result_array["code"] == 200) {
                    echo "✅ 查课结果获取成功\n";
                    echo "查课结果: " . json_encode($result_array, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
                } else {
                    echo "❌ 查课结果获取失败: " . ($result_array["message"] ?? "未知错误") . "\n";
                }
            }
        } else {
            echo "❌ 查课请求失败: " . ($query_result_array["message"] ?? "未知错误") . "\n";
            echo "响应内容: " . substr($query_result, 0, 500) . "\n";
        }
    }
} else {
    echo "\n步骤4：项目无需查课，跳过查课测试\n";
}

// 步骤5：测试下单功能
echo "\n步骤5：测试下单功能\n";

// 使用测试账号密码
$test_user = "testuser123";
$test_pass = "testpass123";

echo "下单账号: {$test_user}\n";
echo "下单密码: {$test_pass}\n";

// 构建下单数据
if ($test_project['isSearchCourse'] == '0') {
    // 无需查课的项目
    $order_data = array(
        "websiteNumber" => $test_project['number'],
        "data" => array(array(
            "username" => $test_user,
            "password" => $test_pass
        ))
    );
} else {
    // 需要查课的项目，使用模拟课程数据
    $order_data = array(
        "websiteNumber" => $test_project['number'],
        "data" => array(array(
            "username" => $test_user,
            "password" => $test_pass,
            "name" => $test_user . "----" . $test_pass,
            "children" => array(array(
                "name" => "测试课程",
                "disabled" => false,
                "id" => "test_course_001",
                "selected" => true
            )),
            "selected" => true
        ))
    );
}

$order_url = "{$huoyuan["url"]}/api/order/buy";

echo "下单URL: {$order_url}\n";
echo "下单数据: " . json_encode($order_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $order_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    "Content-Type: application/json",
    "Authorization: Bearer {$token}"
));
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

$order_result = curl_exec($ch);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "下单HTTP状态码: {$http_code}\n";

if ($curl_error) {
    echo "❌ 下单请求失败: {$curl_error}\n";
} else {
    echo "下单响应: " . $order_result . "\n";
    
    $order_result_array = json_decode($order_result, true);
    if ($order_result_array) {
        echo "下单结果解析: " . json_encode($order_result_array, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        
        if ($order_result_array["code"] == 200) {
            echo "✅ 下单请求成功\n";
            
            // 检查订单ID获取逻辑
            if (isset($order_result_array["data"]["orderList"]) && is_array($order_result_array["data"]["orderList"])) {
                echo "订单列表: " . json_encode($order_result_array["data"]["orderList"], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
                
                foreach ($order_result_array["data"]["orderList"] as $order) {
                    if (isset($order["orderId"]) && $order["username"] == $test_user) {
                        echo "✅ 获取到订单ID: {$order["orderId"]}\n";
                        break;
                    }
                }
            } else {
                echo "❌ 未找到订单列表或订单列表格式异常\n";
                echo "数据结构: " . json_encode($order_result_array["data"], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
            }
        } else {
            echo "❌ 下单失败: " . ($order_result_array["message"] ?? "未知错误") . "\n";
        }
    } else {
        echo "❌ 下单响应解析失败\n";
    }
}

echo "\n=== 测试完成 ===\n";
echo "如果下单测试失败，请检查：\n";
echo "1. API地址是否正确\n";
echo "2. Token是否有效\n";
echo "3. 下单数据格式是否正确\n";
echo "4. 网络连接是否正常\n";

echo "</pre>\n";
?>
