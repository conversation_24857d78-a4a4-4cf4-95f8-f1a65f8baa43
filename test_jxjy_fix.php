<?php
/**
 * 测试易教育修复效果
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "测试易教育修复效果...\n";

// 包含必要文件
include('confing/common.php');

// 手动定义 get_url 函数
function get_url($url, $post = false, $cookie = false, $header = false) {
    $ch = curl_init();
    if ($header) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    } else {
        curl_setopt($ch, CURLOPT_HEADER, 0);
    }
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.62 Safari/537.36");
    if ($post) {
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post));
    }
    if ($cookie) {
        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
    }
    $result = curl_exec($ch);
    curl_close($ch);
    return $result;
}

// 包含进度查询函数
include('Checkorder/jdjk.php');

echo "1. 查询有问题的易教育订单...\n";

// 查询状态为"1"的易教育订单
$sql = "SELECT o.oid, o.user, o.status, o.process, o.yid, h.pt 
        FROM qingka_wangke_order o 
        LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
        WHERE h.pt = 'jxjy' 
        AND (o.status = '1' OR o.process = '1')
        ORDER BY o.addtime DESC 
        LIMIT 3";

$result = $DB->query($sql);
$problem_orders = [];

if ($result) {
    while ($order = $DB->fetch($result)) {
        $problem_orders[] = $order;
    }
}

if (empty($problem_orders)) {
    echo "✅ 没有发现状态为'1'的问题订单\n";
    
    // 查询最新的易教育订单进行测试
    $sql2 = "SELECT o.oid, o.user, o.status, o.process, o.yid, h.pt 
             FROM qingka_wangke_order o 
             LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
             WHERE h.pt = 'jxjy' 
             ORDER BY o.addtime DESC 
             LIMIT 1";
    
    $result2 = $DB->query($sql2);
    if ($result2) {
        $order = $DB->fetch($result2);
        if ($order) {
            $problem_orders[] = $order;
            echo "使用最新的易教育订单进行测试: {$order['oid']}\n";
        }
    }
}

if (!empty($problem_orders)) {
    echo "找到 " . count($problem_orders) . " 个订单进行测试\n";
    
    foreach ($problem_orders as $order) {
        echo "\n2. 测试订单 {$order['oid']}...\n";
        echo "   用户: {$order['user']}\n";
        echo "   当前状态: {$order['status']}\n";
        echo "   当前进度: {$order['process']}\n";
        echo "   订单号: {$order['yid']}\n";
        
        try {
            echo "   调用 processCx 函数...\n";
            $progress_result = processCx($order['oid']);
            
            if (is_array($progress_result) && !empty($progress_result)) {
                foreach ($progress_result as $i => $item) {
                    echo "   结果 $i:\n";
                    if (is_array($item)) {
                        foreach ($item as $key => $value) {
                            echo "     $key: $value\n";
                        }
                        
                        // 如果查询成功，更新数据库
                        if (isset($item['code']) && $item['code'] == 1) {
                            echo "   ✅ 进度查询成功，更新数据库...\n";
                            
                            $update_sql = "UPDATE qingka_wangke_order SET 
                                          status = ?, 
                                          process = ?, 
                                          remarks = ?,
                                          uptime = NOW()
                                          WHERE oid = ?";
                            
                            $update_result = $DB->prepare_query($update_sql, [
                                $item['status_text'],
                                $item['process'],
                                $item['remarks'],
                                $order['oid']
                            ]);
                            
                            if ($update_result) {
                                echo "   ✅ 数据库更新成功\n";
                            } else {
                                echo "   ❌ 数据库更新失败\n";
                            }
                        }
                    } else {
                        echo "     非数组项: $item\n";
                    }
                }
            } else {
                echo "   ❌ processCx 返回空结果或非数组\n";
                var_dump($progress_result);
            }
            
        } catch (Exception $e) {
            echo "   ❌ 异常: " . $e->getMessage() . "\n";
        }
    }
} else {
    echo "❌ 没有找到易教育订单进行测试\n";
}

echo "\n3. 测试同步API...\n";
try {
    $_GET['action'] = 'sync_orders';
    ob_start();
    include('api/jxjyapi.php');
    $api_output = ob_get_clean();
    echo "API输出: $api_output\n";
} catch (Exception $e) {
    echo "❌ API测试失败: " . $e->getMessage() . "\n";
}

echo "\n🎉 测试完成！\n";
?>
