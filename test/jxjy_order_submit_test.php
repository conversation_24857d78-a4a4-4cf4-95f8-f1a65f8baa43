<?php
/**
 * 易教育订单提交测试
 * 测试订单提交到上游的完整流程
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');
include_once(dirname(__FILE__) . '/../Checkorder/xdjk.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育订单提交测试</h1>\n";
echo "<pre>\n";

// 获取易教育货源配置
$jxjy_huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
if (!$jxjy_huoyuan) {
    echo "❌ 未找到易教育货源配置\n";
    exit;
}

echo "✅ 易教育货源配置: ID={$jxjy_huoyuan['hid']}, 名称={$jxjy_huoyuan['name']}\n";

// 测试1：检查最近的易教育订单
echo "\n=== 测试1：检查最近的易教育订单 ===\n";
$recent_orders = $DB->query("SELECT oid,user,kcname,yid,status,dockstatus,addtime FROM qingka_wangke_order WHERE hid='{$jxjy_huoyuan['hid']}' ORDER BY oid DESC LIMIT 5");

echo "最近的易教育订单:\n";
$test_order = null;
while ($row = $DB->fetch($recent_orders)) {
    echo "订单 {$row['oid']}: {$row['user']} - {$row['status']} - dockstatus:{$row['dockstatus']} - {$row['addtime']}\n";
    if ($row['dockstatus'] == 0 && !$test_order) {
        $test_order = $row;
    }
}

if (!$test_order) {
    echo "\n⚠️  没有找到dockstatus=0的易教育订单进行测试\n";
    echo "创建一个测试订单...\n";
    
    // 获取一个易教育项目
    $test_project = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking='{$jxjy_huoyuan['hid']}' AND status=1 LIMIT 1");
    if ($test_project) {
        $test_oid = time() . rand(100, 999);
        $current_time = date('Y-m-d H:i:s');
        $insert_sql = "INSERT INTO qingka_wangke_order (uid,cid,hid,ptname,school,name,user,pass,kcid,kcname,courseEndTime,fees,noun,miaoshua,addtime,ip,dockstatus,status,process) VALUES ('1','{$test_project['cid']}','{$jxjy_huoyuan['hid']}','{$test_project['name']}','自动识别','测试用户','test_user','test_pass','test_course_id','测试课程','2025-12-31','1.00','{$test_project['noun']}','0','{$current_time}','127.0.0.1','0','已提交','0%')";

        if ($DB->query($insert_sql)) {
            $test_order = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE uid='1' AND user='test_user' AND addtime='{$current_time}' LIMIT 1");
            echo "✅ 创建测试订单成功: {$test_order['oid']}\n";
        } else {
            echo "❌ 创建测试订单失败: " . $DB->error() . "\n";
            exit;
        }
    } else {
        echo "❌ 没有找到易教育项目\n";
        exit;
    }
}

echo "\n使用测试订单: {$test_order['oid']}\n";

// 测试2：检查addWk函数
echo "\n=== 测试2：检查addWk函数 ===\n";
if (function_exists('addWk')) {
    echo "✅ addWk函数存在\n";
} else {
    echo "❌ addWk函数不存在\n";
    exit;
}

// 测试3：模拟调用addWk函数
echo "\n=== 测试3：模拟调用addWk函数 ===\n";
echo "调用 addWk({$test_order['oid']})\n";

try {
    $result = addWk($test_order['oid']);
    
    echo "\n提交结果:\n";
    echo "  code: " . (isset($result['code']) ? $result['code'] : 'undefined') . "\n";
    echo "  msg: " . (isset($result['msg']) ? $result['msg'] : 'undefined') . "\n";
    
    if (isset($result['yid'])) {
        echo "  yid: " . $result['yid'] . "\n";
    }
    
    if (isset($result['code'])) {
        if ($result['code'] == 1) {
            echo "  ✅ 订单提交成功\n";
            
            // 更新订单状态
            $update_sql = "UPDATE qingka_wangke_order SET status='上号中', dockstatus=1, yid='{$result['yid']}' WHERE oid='{$test_order['oid']}'";
            if ($DB->query($update_sql)) {
                echo "  ✅ 订单状态更新成功\n";
            } else {
                echo "  ❌ 订单状态更新失败\n";
            }
        } else {
            echo "  ❌ 订单提交失败: " . $result['msg'] . "\n";
            
            // 分析失败原因
            if (strpos($result['msg'], '项目信息不存在') !== false) {
                echo "\n失败原因分析:\n";
                echo "  - 项目信息查询失败\n";
                echo "  - 检查xdjk.php中的项目查询逻辑\n";
            } elseif (strpos($result['msg'], 'Token') !== false) {
                echo "\n失败原因分析:\n";
                echo "  - Token相关问题\n";
                echo "  - 检查易教育货源的Token配置\n";
            } elseif (strpos($result['msg'], '网络') !== false) {
                echo "\n失败原因分析:\n";
                echo "  - 网络连接问题\n";
                echo "  - 检查易教育API是否可访问\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "  ❌ addWk函数调用异常: " . $e->getMessage() . "\n";
}

// 测试4：检查Redis队列
echo "\n=== 测试4：检查Redis队列 ===\n";
try {
    $redis = new Redis();
    $redis->connect("127.0.0.1", "6379");
    $redis->select(10); // addoid队列在分区10
    
    if ($redis->ping()) {
        echo "✅ Redis连接正常\n";
        
        // 检查addoid队列长度
        $queue_length = $redis->lLen('addoid');
        echo "addoid队列长度: {$queue_length}\n";
        
        // 检查是否有dockstatus=0的订单
        $pending_orders = $DB->get_row("SELECT COUNT(*) as count FROM qingka_wangke_order WHERE dockstatus=0");
        echo "待提交订单数量: {$pending_orders['count']}\n";
        
        if ($pending_orders['count'] > 0) {
            echo "✅ 有待提交订单，addru.php会将其加入队列\n";
        } else {
            echo "ℹ️ 没有待提交订单\n";
        }
    } else {
        echo "❌ Redis连接失败\n";
    }
} catch (Exception $e) {
    echo "❌ Redis连接异常: " . $e->getMessage() . "\n";
}

// 测试5：检查守护进程文件
echo "\n=== 测试5：检查守护进程文件 ===\n";
$daemon_files = array(
    'redis/addru.php' => '新订单入队脚本',
    'redis/addchu.php' => '订单提交处理脚本'
);

foreach ($daemon_files as $file => $description) {
    if (file_exists(dirname(__FILE__) . '/../' . $file)) {
        echo "✅ {$description} 存在: {$file}\n";
    } else {
        echo "❌ {$description} 不存在: {$file}\n";
    }
}

// 测试总结
echo "\n=== 测试总结 ===\n";
echo "易教育订单提交流程:\n";
echo "1. 用户下单 -> 订单插入数据库，dockstatus=0\n";
echo "2. addru.php定时扫描 -> 将dockstatus=0的订单加入addoid队列\n";
echo "3. addchu.php守护进程 -> 从addoid队列取订单，调用addWk()函数\n";
echo "4. addWk()函数 -> 调用易教育API提交订单到上游\n";
echo "5. 提交成功 -> 更新订单状态为'上号中'，dockstatus=1\n";

echo "\n修复内容:\n";
echo "1. ✅ 修复了api/jxjyapi.php中的dockstatus设置\n";
echo "2. ✅ 修复了Checkorder/xdjk.php中的项目查询逻辑\n";
echo "3. ✅ 确保易教育订单能正确进入提交队列\n";

echo "\n如果订单仍无法提交到上游，请检查:\n";
echo "1. Redis守护进程是否正常运行\n";
echo "2. addru.php和addchu.php是否正常执行\n";
echo "3. 易教育API是否可正常访问\n";
echo "4. Token是否有效\n";

echo "\n</pre>\n";
?>
