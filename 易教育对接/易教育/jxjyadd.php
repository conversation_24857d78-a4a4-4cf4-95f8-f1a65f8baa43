<?php
require_once 'jxjyyong.php';
?>
<link rel="stylesheet" href="assets/element/element.css">

<body>
    <div id="submit">
        <div class="layui-fluid">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md4">
                    <div class="layui-card">
                        <div class="layui-card-header">公告</div>
                        <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
                            <div class="layui-form-item">
                                <span class="help-block m-b-none" style="color:red;">
                                    <h1>致站长 会玩就玩</h1><br>
                                    玩不明白就花钱解决<br>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card">
                        <div class="layui-card-header">提交订单</div>
                        <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
                            <div class="layui-form-item">
                                <el-select id="select" v-model="id" @change="tips(id)" popper-class="lioverhide" :popper-append-to-body="false" filterable placeholder="请先选择平台，输入可进行搜索" style="max-height: 99vh; width:100%">
                                    <el-option v-for="class2 in class1" :key="class2.id" :label="class2.name+'-'+class2.url+''" :value="class2.id">
                                    </el-option>
                                </el-select>
                            </div>
                            <div v-show="show">
                                <div class="layui-form-item">
                                    <label class="layadmin-user-login-icon layui-icon layui-icon-cellphone" for="LAY-user-login-cellphone"></label>
                                    <input type="text" name="user" placeholder="请输入手机号" class="layui-input" v-model="user">
                                </div>
                                <div class="layui-form-item">
                                    <label class="layadmin-user-login-icon layui-icon layui-icon-password" for="LAY-user-login-cellphone"></label>
                                    <input type="text" name="pass" placeholder="请输入密码" class="layui-input" v-model="pass">
                                </div>
                                <div class="layui-form-item">
                                    <span class="help-block m-b-none">名称： <span v-html="name"></span></span><br>
                                    <span class="help-block m-b-none">网址： <span v-html="url"></span></span><br>
                                    <span class="help-block m-b-none">格式： <span v-html="format"></span></span><br>
                                    <span class="help-block m-b-none">价格： <span style="color:red;" v-html="price"></span></span><br>
                                    <span class="help-block m-b-none">速率： <span v-html="rate"></span></span><br>
                                    <span class="help-block m-b-none">考试： <span v-html="isExam"></span></span><br>
                                    <span class="help-block m-b-none">说明： <span v-html="description"></span></span><br>
                                </div>
                                <button type="button" @click="add" class="layui-btn">立即添加</button>
                                <button type="button" @click="get" class="layui-btn layui-bg-blue" v-show="ckshow">查询课程</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md8" v-show="kcshow">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            <div style="display: flex;justify-content: space-between;align-items: center">
                                <div class="card-title">课程信息 <b>{{kcinfo.userinfo}}</b> <span v-if="kcinfo.msg=='查询成功'"><b style="color: green;">{{kcinfo.msg}}</b></span>
                                    <span v-else-if="kcinfo.msg!='查询成功'"><b style="color: red;">{{kcinfo.msg}}</b></span>
                                </div>
                            </div>
                        </div>
                        <div class="layui-card-body" style="padding: 0">
                            <el-table ref="tableRef" :data="kcinfo.data" @selection-change="handleSelectionChange" style="width: 100%">
                                <el-table-column type="selection" width="100" align="center"></el-table-column>
                                <el-table-column label="课程名称">
                                    <template #default="scope">{{ scope.row.name }}</template>
                                </el-table-column>
                                <el-table-column label="课程ID" width="100">
                                    <template #default="scope">{{ scope.row.id }}</template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md8">
                    <div class="layui-card">
                        <div class="layui-card-header">进度详情</div>
                        <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md3 layui-col-sm6 layui-col-xs6">
                                    <el-select id="select" v-model="cx.status_text" filterable placeholder="请选择状态" style="background: url('../index/arrow.png') no-repeat scroll 99%;width:100%">
                                        <el-option label="请选择状态" value=""></el-option>
                                        <el-option label="待更新" value="待更新"></el-option>
                                        <el-option label="队列中" value="队列中"></el-option>
                                        <el-option label="完成" value="完成"></el-option>
                                        <el-option label="运行中" value="运行中"></el-option>
                                        <el-option label="异常" value="异常"></el-option>
                                        <el-option label="正在售后" value="正在售后"></el-option>
                                        <el-option label="售后完成" value="售后完成"></el-option>
                                        <el-option label="人工处理" value="人工处理"></el-option>
                                        <el-option label="已退款" value="已退款"></el-option>
                                        <el-option label="暂停中" value="暂停中"></el-option>
                                        <el-option label="已暂停" value="已暂停"></el-option>
                                    </el-select>
                                </div>
                                <div class="layui-col-md3 layui-col-sm6 layui-col-xs6">
                                    <el-select id="select" v-model="cx.pagesize" filterable placeholder="选择每页订单数量" style="background: url('../index/arrow.png') no-repeat scroll 99%;width:100%">
                                        <el-option label="选择每页订单数量" value=""></el-option>
                                        <el-option label="20/页" value="20"></el-option>
                                        <el-option label="50/页" value="50"></el-option>
                                        <el-option label="100/页" value="100"></el-option>
                                        <el-option label="200/页" value="200"></el-option>
                                        <el-option label="500/页" value="500"></el-option>
                                        <el-option label="1000/页" value="1000"></el-option>
                                    </el-select>
                                </div>
                                <div class="layui-col-md3 layui-col-sm6 layui-col-xs6">
                                    <input type="text" v-model="cx.status_text1" value="" class="layui-input" placeholder="自定义状态查询" required />
                                </div>
                            </div>
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md3 layui-col-sm6 layui-col-xs6">
                                    <input type="text" v-model="cx.oid" value="" class="layui-input" placeholder="请输入订单ID" required />
                                </div>
                                <div class="layui-col-md3 layui-col-sm6 layui-col-xs6">
                                    <input type="text" v-model="cx.phone" value="" class="layui-input" placeholder="请输入下单账号" required />
                                </div>
                                <?php if ($userrow['uid'] == 1) { ?>
                                    <div class="layui-col-md3 layui-col-sm6 layui-col-xs6" v-if="row.uid==1">
                                        <input type="text" v-model="cx.uid" value="" class="layui-input" placeholder="请输入UID" required />
                                    </div>
                                <?php } ?>
                                <div class="layui-col-md3 layui-col-sm6 layui-col-xs6">
                                    <input type="submit" value="查询订单" @click="orderlist(1)" class="layui-btn" />
                                </div>

                            </div>
                            <div class="layui-form-item" v-for="res in row.data">
                                <blockquote class="layui-elem-quote">
                                    <div class="layui-card-body layui-bg-gray">
                                        <h3><b>[账号] <span @click="kmm(res.pass)">{{res.user}}</span></b>
                                            <span class="layui-badge layui-bg-blue" v-if="res.status!=''">{{res.status}}</span>
                                        </h3>
                                        <b>订单ID：</b>{{res.oid}} <br>
                                        <span v-if="row.uid==1"><b>UID：</b> {{res.uid}} <br></span>
                                        <b>项目：</b>{{res.ptname}} <br>
                                        <span v-if="res.kcname!=''"><b>课程：</b> {{res.kcname}} <br></span>
                                        <span v-if="res.kcid!=''"><b>课程ID：</b> {{res.kcid}} <br></span>
                                        <span v-if="res.process!=''"><b>订单进度：</b> {{res.process}} <br></span>
                                        <b>提交时间：</b>{{res.addtime}} <br>
                                        <b>账号操作：</b>
                                        <button class="layui-btn layui-btn-xs" @click="jxjyup(res.oid)">同步进度</button>
                                        <button class="layui-btn layui-btn-xs layui-bg-red" @click="jxjygmbs(res.oid, res.pass)">改密补刷</button>
                                    </div>
                                </blockquote>
                            </div>
                            <div class="layui-btn-container" v-if="row.last_page>1">
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" @click="orderlist(1)">首页</button>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm"><i class="layui-icon"></i></button>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" @click="orderlist(row.current_page-3)" v-if="row.current_page-3>=1">{{ row.current_page-3 }}</button>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" @click="orderlist(row.current_page-2)" v-if="row.current_page-2>=1">{{ row.current_page-2 }}</button>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" @click="orderlist(row.current_page-1)" v-if="row.current_page-1>=1">{{ row.current_page-1 }}</button>
                                <button type="button" :class="{'layui-btn layui-btn-sm':row.current_page==row.current_page}" @click="orderlist(row.current_page)" v-if="row.current_page">{{ row.current_page }}</button>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" @click="orderlist(row.current_page+1)" v-if="row.current_page+1<=row.last_page">{{ row.current_page+1 }}</button>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" @click="orderlist(row.current_page+2)" v-if="row.current_page+2<=row.last_page">{{ row.current_page+2 }}</button>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" @click="orderlist(row.current_page+3)" v-if="row.current_page+3<=row.last_page">{{ row.current_page+3 }}</button>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" @click="row.last_page>row.current_page?orderlist(row.current_page+1):''"><i class="layui-icon"></i></button>
                                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" @click="orderlist(row.last_page)">尾页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="assets/js/bootstrap.min.js"></script>
<script src="//cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>
<script src="assets/layer/3.1.1/layer.js"></script>
<script src="assets/js/vue.min.js"></script>
<script src="assets/js//vue-resource.min.js"></script>
<script src="assets/js//axios.min.js"></script>
<script src="assets/element/element.js"></script>

<script>
    var vm = new Vue({
        el: "#submit",
        data: {
            row: '',
            id: '',
            show: false,
            name: '',
            format: '',
            url: '',
            price: '',
            rate: '',
            isExam: '',
            description: '',
            ckshow: '',
            kcshow: false,
            kcinfo: {
                data: [],
                userinfo: '',
                must: '1',
                msg: '',
            },
            check_row: [],
            user: '',
            pass: '',
            userinfo: '',
            class1: '',
            cx: {
                status_text: '',
                dock: '',
                phone: '',
                oid: '',
                pagesize: '',
            }
        },
        methods: {
            getclass: function() {
                this.loading = true;
                this.$http.post("/api/jxjyapi.php?act=jxjyclass").then(function(data) {
                    this.loading = false;
                    if (data.data.code == 1) {
                        this.class1 = data.body.data;
                    } else {
                        this.$message.error(data.data.msg);
                    }
                });
            },
            handleSelectionChange(selection) {
                this.check_row = selection;
            },
            get: function() {
                if (this.id == '' || this.user == '' || this.pass == '') {
                    layer.msg("所有项目不能为空");
                    return false;
                }
                this.kcinfo.data = [];
                this.kcinfo.userinfo = '';
                this.check_row = [];

                var loading = layer.load(2);
                this.$http.post("/api/jxjyapi.php?act=getcourse", {
                    id: this.id,
                    user: this.user,
                    pass: this.pass
                }, {
                    emulateJSON: true
                }).then(function(response) {
                    layer.close(loading);
                    this.kcshow = true;
                    if (response.data[0].code === 1) {
                        this.kcinfo.data = response.data[0].data;
                        this.kcinfo.userinfo = response.data[0].userinfo;
                        this.kcinfo.msg = response.data[0].msg;
                    } else {
                        this.kcinfo.userinfo = response.data[0].userinfo;
                        this.kcinfo.msg = response.data[0].msg;
                    }
                });
            },
            add: function() {
                if (this.id == '' || this.user == '' || this.pass == '') {
                    layer.msg("所有项目不能为空");
                    return false;
                } else if (this.kcinfo.must === '1' && this.check_row == '') {
                    layer.msg("请选择要提交的课程");
                    return false;
                }
                var loading = layer.load(2);
                this.$http.post("/api/jxjyapi.php?act=jxjyadd", {
                    id: this.id,
                    user: this.user,
                    pass: this.pass,
                    select: this.check_row,
                }, {
                    emulateJSON: true
                }).then(function(response) {
                    layer.close(loading);
                    if (response.data && response.data.code === 1) {
                        layer.msg(response.data.msg, {
                            icon: 1
                        });
                        this.kcinfo.data = [];
                        this.kcinfo.userinfo = '';
                        this.check_row = [];
                        this.kcshow == false;
                        vm.orderlist(vm.row.current_page);
                    } else {
                        layer.msg(response.data.msg, {
                            icon: 2
                        });
                    }
                });
            },
            orderlist: function(page) {
                var load = layer.load(2);
                data = {
                    cx: this.cx,
                    page
                }
                this.$http.post("/api/jxjyapi.php?act=jxjyorderlist", data, {
                    emulateJSON: true
                }).then(function(data) {
                    layer.close(load);
                    if (data.data.code == 1) {
                        this.row = data.body;
                    } else {
                        layer.msg(data.data.msg, {
                            icon: 2
                        });
                    }
                });
            },
            jxjyup: function(oid) {
                var load = layer.load(2);
                layer.msg("正在努力获取中....", {
                    icon: 3
                });
                $.get("/api/jxjyapi.php?act=jxjyuporder&oid=" + oid, function(data) {
                    layer.close(load);
                    if (data.code == 1) {
                        vm.orderlist(vm.row.current_page);
                        layer.msg(data.msg, {
                            icon: 1
                        });
                    } else {
                        layer.msg(data.msg, {
                            icon: 2
                        });
                    }
                });
            },
            jxjygmbs: function(oid, originalPass) {
                var self = this;
                layer.prompt({
                    title: '修改密码（无需修改点击确定就行）',
                    formType: 3,
                    value: originalPass
                }, function(newpass, index) {
                    layer.close(index);
                    var load = layer.load(2);
                    $.post("/api/jxjyapi.php?act=jxjxgmbs&oid=" + oid, {
                        newpass
                    }, function(data) {
                        layer.close(load);
                        if (data.code == 1) {
                            vm.orderlist(vm.row.current_page);
                            layer.msg(data.msg, {
                                icon: 1
                            });
                        } else {
                            layer.msg(data.msg, {
                                icon: 2
                            });
                        }
                    });
                });
            },
            tips: function(message) {
                for (var i = 0; this.class1.length > i; i++) {
                    if (this.class1[i].id == message) {
                        this.show = true;
                        this.id = message;
                        this.name = this.class1[i].name;
                        this.format = this.class1[i].format;
                        this.url = this.class1[i].url;
                        this.price = this.class1[i].price + '/' + this.class1[i].unit;
                        this.rate = this.class1[i].rate === 0 ? '正常' : this.class1[i].rate === 1 ? '加速' : '秒学';
                        this.isExam = this.class1[i].isExam === 0 ? '不支持' : '支持';
                        this.ckshow = this.class1[i].isSearchCourse === '1' ? true : false;
                        this.kcinfo.must = this.class1[i].must;
                        this.description = this.class1[i].description;
                        this.id = this.class1[i].id;
                        return false;
                    }
                }
            },
            kmm: function(pass) {
                layer.confirm(pass, {
                    title: '密码',
                    icon: 1,
                    btn: ['确定'] //按钮
                });
            },
        },
        mounted() {
            this.getclass();
            this.orderlist(1);
        }
    });
</script>

</html>