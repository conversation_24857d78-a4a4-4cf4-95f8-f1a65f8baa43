# 🚀 宝塔面板Redis守护进程配置完整指南

## 📋 概述
本指南将帮您在宝塔面板中为redis文件夹中的所有PHP脚本配置守护进程，确保网课代刷系统的队列处理功能正常运行。

## 🔍 需要配置的文件分析

### 持续运行的守护进程（24小时运行）
- `chusheng1.php` - 主要队列处理器1（处理多个队列池）
- `chusheng2.php` - 主要队列处理器2（处理多个队列池）
- `chusheng3.php` - 主要队列处理器3（处理特定队列池）
- `addchu.php` - 订单添加处理器（处理新订单）
- `chushengplbs.php` - 批量补刷处理器
- `chushengplms.php` - 批量秒刷处理器
- `chushengpltx.php` - 批量停止处理器

### 定时执行的入队脚本（定时运行）
- `aaru.php` - 订单状态入队（每5分钟）
- `addru.php` - 新订单入队（每3分钟）
- `bbru.php` - 订单状态更新入队（每10分钟）
- `ccru.php` - 进行中订单入队（每5分钟）
- `ddru.php` - 待考试订单入队（每10分钟）
- `eeru.php` - 其他状态订单入队（每15分钟）

## 🛠️ 方法一：使用宝塔面板进程守护管理器

### 第一步：安装进程守护管理器
1. 登录宝塔面板
2. 点击 **"软件商店"**
3. 搜索 **"进程守护管理器"**
4. 点击安装

### 第二步：添加守护进程
在进程守护管理器中添加以下守护进程：

#### 1. Redis队列处理器1
```
名称: Redis队列处理器1
启动文件: /usr/bin/php
启动参数: /www/wwwroot/117.72.158.75/redis/chusheng1.php
运行目录: /www/wwwroot/117.72.158.75/redis
进程数量: 1
自动重启: 是
```

#### 2. Redis队列处理器2
```
名称: Redis队列处理器2
启动文件: /usr/bin/php
启动参数: /www/wwwroot/117.72.158.75/redis/chusheng2.php
运行目录: /www/wwwroot/117.72.158.75/redis
进程数量: 1
自动重启: 是
```

#### 3. Redis队列处理器3
```
名称: Redis队列处理器3
启动文件: /usr/bin/php
启动参数: /www/wwwroot/117.72.158.75/redis/chusheng3.php
运行目录: /www/wwwroot/117.72.158.75/redis
进程数量: 1
自动重启: 是
```

#### 4. Redis订单添加处理器
```
名称: Redis订单添加处理器
启动文件: /usr/bin/php
启动参数: /www/wwwroot/117.72.158.75/redis/addchu.php
运行目录: /www/wwwroot/117.72.158.75/redis
进程数量: 1
自动重启: 是
```

#### 5. Redis批量补刷处理器
```
名称: Redis批量补刷处理器
启动文件: /usr/bin/php
启动参数: /www/wwwroot/117.72.158.75/redis/chushengplbs.php
运行目录: /www/wwwroot/117.72.158.75/redis
进程数量: 1
自动重启: 是
```

#### 6. Redis批量秒刷处理器
```
名称: Redis批量秒刷处理器
启动文件: /usr/bin/php
启动参数: /www/wwwroot/117.72.158.75/redis/chushengplms.php
运行目录: /www/wwwroot/117.72.158.75/redis
进程数量: 1
自动重启: 是
```

#### 7. Redis批量停止处理器
```
名称: Redis批量停止处理器
启动文件: /usr/bin/php
启动参数: /www/wwwroot/117.72.158.75/redis/chushengpltx.php
运行目录: /www/wwwroot/117.72.158.75/redis
进程数量: 1
自动重启: 是
```

### 第三步：添加定时任务
在宝塔面板的 **"计划任务"** 中添加：

#### 1. 订单状态入队任务
```
任务类型: Shell脚本
任务名称: Redis订单状态入队
执行周期: 每5分钟
脚本内容: /usr/bin/php /www/wwwroot/117.72.158.75/redis/aaru.php
```

#### 2. 新订单入队任务
```
任务类型: Shell脚本
任务名称: Redis新订单入队
执行周期: 每3分钟
脚本内容: /usr/bin/php /www/wwwroot/117.72.158.75/redis/addru.php
```

#### 3. 订单状态更新入队
```
任务类型: Shell脚本
任务名称: Redis订单状态更新入队
执行周期: 每10分钟
脚本内容: /usr/bin/php /www/wwwroot/117.72.158.75/redis/bbru.php
```

#### 4. 进行中订单入队
```
任务类型: Shell脚本
任务名称: Redis进行中订单入队
执行周期: 每5分钟
脚本内容: /usr/bin/php /www/wwwroot/117.72.158.75/redis/ccru.php
```

#### 5. 待考试订单入队
```
任务类型: Shell脚本
任务名称: Redis待考试订单入队
执行周期: 每10分钟
脚本内容: /usr/bin/php /www/wwwroot/117.72.158.75/redis/ddru.php
```

#### 6. 其他状态订单入队
```
任务类型: Shell脚本
任务名称: Redis其他状态订单入队
执行周期: 每15分钟
脚本内容: /usr/bin/php /www/wwwroot/117.72.158.75/redis/eeru.php
```

## 🛠️ 方法二：使用一键配置脚本

### 执行一键配置脚本
```bash
# 进入项目目录
cd /www/wwwroot/117.72.158.75

# 给脚本执行权限
chmod +x setup_redis_daemons.sh

# 执行配置脚本
./setup_redis_daemons.sh
```

## 📊 监控和管理

### 查看守护进程状态
```bash
# 查看所有Redis服务状态
systemctl status redis-*

# 查看特定服务状态
systemctl status redis-chusheng1

# 查看服务日志
journalctl -u redis-chusheng1 -f
```

### 管理守护进程
```bash
# 重启所有Redis服务
systemctl restart redis-*

# 停止所有Redis服务
systemctl stop redis-*

# 启动所有Redis服务
systemctl start redis-*
```

### 查看定时任务日志
```bash
# 查看定时任务执行日志
tail -f /www/wwwroot/117.72.158.75/logs/redis_cron.log

# 查看具体脚本日志
tail -f /www/wwwroot/117.72.158.75/logs/aaru.log
```

## ⚠️ 注意事项

### 1. 确保Redis服务运行
```bash
# 检查Redis是否运行
redis-cli ping

# 如果返回PONG则表示Redis正常运行
```

### 2. 检查PHP路径
确认PHP可执行文件路径，常见路径：
- `/usr/bin/php`
- `/www/server/php/74/bin/php` (PHP 7.4)
- `/www/server/php/80/bin/php` (PHP 8.0)

### 3. 权限设置
确保www用户有执行权限：
```bash
chown -R www:www /www/wwwroot/117.72.158.75/redis/
chmod +x /www/wwwroot/117.72.158.75/redis/*.php
```

### 4. 防火墙设置
确保Redis端口6379在内网可访问（不要对外开放）

## 🔧 故障排除

### 1. 服务启动失败
- 检查PHP路径是否正确
- 检查文件权限
- 查看系统日志：`journalctl -u redis-chusheng1`

### 2. Redis连接失败
- 检查Redis服务是否启动
- 检查Redis配置文件
- 确认端口6379可访问

### 3. 定时任务不执行
- 检查crontab是否正确添加
- 查看系统cron日志：`tail -f /var/log/cron`
- 确认脚本有执行权限

## 📈 性能优化建议

1. **监控资源使用**：定期检查CPU和内存使用情况
2. **调整进程数量**：根据服务器性能调整守护进程数量
3. **优化定时任务频率**：根据业务需求调整执行频率
4. **日志轮转**：设置日志文件自动清理，避免占用过多磁盘空间

## ✅ 配置完成检查清单

- [ ] 安装进程守护管理器插件
- [ ] 添加7个守护进程
- [ ] 添加6个定时任务
- [ ] 确认Redis服务运行正常
- [ ] 确认所有守护进程启动成功
- [ ] 确认定时任务正常执行
- [ ] 设置日志监控
- [ ] 测试系统功能正常

配置完成后，您的网课代刷系统的Redis队列处理功能将自动运行，确保订单处理的稳定性和效率。
