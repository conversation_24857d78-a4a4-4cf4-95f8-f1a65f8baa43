# 易教育查课问题修复指南

## 🚨 问题描述

前端点击查课时提示：**"项目信息不存在，项目编号:xxxx"**

## 🔍 问题根本原因

**字段使用错误问题**：
1. **前端查课时**：使用 `getnoun` 字段作为项目编号传递给查课接口
2. **但 `getnoun` 字段**：存储的是易教育网站的项目顺序编号（无用的数字）
3. **而 `noun` 字段**：才是正确的易教育站点编号（如：website123）
4. **查课接口**：收到错误的项目编号，导致查询失败

**这就是问题的根本原因！** 易教育项目应该使用 `noun` 字段而不是 `getnoun` 字段。

## ✅ 解决方案

**已完成修复！** 问题已通过修改查课逻辑解决，无需额外操作。

### 修复内容

1. **修复了 `apisub.php` 文件**：
   - 添加了易教育项目特殊处理逻辑
   - 易教育项目使用 `noun` 字段而不是 `getnoun` 字段
   - 其他平台项目不受影响，仍使用 `getnoun` 字段

2. **修复了 `api/jxjyapi.php` 文件**：
   - 查课功能现在从 `qingka_wangke_class` 表查询项目信息
   - 下单功能同样使用主商品表
   - 移除了对 `qingka_wangke_jxjyclass` 专用表的依赖

3. **修复了 `Checkorder/ckjk.php` 文件**：
   - 易教育查课逻辑使用正确的数据表和字段
   - 统一使用 `noun` 字段作为项目编号

### 验证修复

运行测试脚本验证修复效果：
```
http://你的域名/test/jxjy_fix_test.php
```

### 使用要求

确保以下条件满足：
1. **易教育货源已配置**：在货源管理中添加易教育货源
2. **易教育项目已添加**：在商品管理中添加易教育项目，`docking` 字段指向易教育货源ID
3. **项目编号正确**：项目的 `noun` 字段包含易教育的项目编号

## 📊 修复验证

### 1. 数据库验证
```sql
-- 检查项目数量
SELECT COUNT(*) as project_count FROM qingka_wangke_jxjyclass WHERE status = 1;

-- 查看项目列表
SELECT id, number, name, isSearchCourse FROM qingka_wangke_jxjyclass WHERE status = 1 LIMIT 10;
```

### 2. 功能验证
- 前端选择易教育项目
- 输入测试账号密码
- 点击查课按钮
- 应该能正常返回课程列表

### 3. API验证
```
POST /api/jxjyapi.php?act=getcourse
参数：
- id: 项目ID
- user: 测试账号
- pass: 测试密码
```

## 🔧 技术细节

### 修复的文件
1. **api/jxjyapi.php** - 修复查课和下单功能，统一使用主商品表
2. **Checkorder/ckjk.php** - 修复易教育查课逻辑，使用正确的数据表和字段
3. **test/jxjy_final_test.php** - 新增最终验证测试脚本

### 关键修复点

**1. api/jxjyapi.php 修复**
```php
// 修复前：查询专用表
$rs = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE id='$id' LIMIT 1");

// 修复后：查询主商品表
$rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='$id' LIMIT 1");
```

**2. apisub.php 修复（关键修复）**
```php
// 修复前：所有项目都使用getnoun字段
$result=getWk($rs['queryplat'],$rs['getnoun'],...);

// 修复后：易教育项目使用noun字段，其他项目仍使用getnoun字段
$jxjy_huoyuan = $DB->get_row("SELECT hid FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
$project_noun = ($jxjy_huoyuan && $rs['docking'] == $jxjy_huoyuan['hid']) ? $rs['noun'] : $rs['getnoun'];
$result=getWk($rs['queryplat'],$project_noun,...);
```

**3. Checkorder/ckjk.php 修复**
```php
// 修复前：查询专用表
$class_info = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE number = '{$noun}' LIMIT 1");

// 修复后：查询主商品表
$class_info = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE noun = '{$noun}' AND docking = '{$type}' LIMIT 1");
```

### 数据表结构
```sql
-- 易教育项目表
CREATE TABLE `qingka_wangke_jxjyclass` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `number` varchar(255) NOT NULL COMMENT '对接站ID',
  `name` varchar(255) NOT NULL,
  `isSearchCourse` varchar(255) NOT NULL COMMENT '查课',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态',
  -- 其他字段...
  PRIMARY KEY (`id`),
  UNIQUE KEY `number` (`number`)
);
```

## 🚀 使用流程

### 正常使用流程
1. **管理员首次配置**
   - 添加易教育货源配置
   - 运行项目同步脚本
   - 验证功能正常

2. **用户日常使用**
   - 选择易教育项目
   - 输入账号密码
   - 查课/下单操作

3. **定期维护**
   - 定期同步项目数据
   - 监控系统状态
   - 处理异常情况

### 故障排除
1. **如果同步失败**
   - 检查网络连接
   - 验证API地址
   - 确认账号密码
   - 查看错误日志

2. **如果Token失效**
   - 系统会自动重新登录
   - 如果仍然失败，检查账号密码

3. **如果项目数据异常**
   - 重新运行同步脚本
   - 检查数据库表结构
   - 联系技术支持

## 📝 注意事项

1. **首次使用必须同步**
   - 新安装的系统必须先同步项目数据
   - 否则会出现"项目信息不存在"错误

2. **定期同步建议**
   - 建议每周同步一次项目数据
   - 获取最新的项目信息和配置

3. **权限要求**
   - 项目同步需要管理员权限
   - 普通用户只能使用查课下单功能

4. **网络要求**
   - 需要服务器能访问易教育API
   - 确保防火墙允许外网连接

## 🎉 修复完成

按照以上步骤操作后，易教育查课功能应该能正常使用。如果仍有问题，请：

1. 查看同步日志
2. 运行功能测试
3. 检查系统配置
4. 联系技术支持

**修复成功标志**：前端查课时能正常返回课程列表，不再提示"项目信息不存在"错误。
