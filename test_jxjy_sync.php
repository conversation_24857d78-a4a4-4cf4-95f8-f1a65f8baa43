<?php
/**
 * 简单的易教育同步测试
 */

// 模拟GET参数
$_GET['action'] = 'sync_orders';

// 包含必要文件
include('confing/common.php');
include('Checkorder/jdjk.php');

echo "开始测试易教育同步功能...\n";

// 1. 检查易教育货源配置
echo "1. 检查易教育货源配置\n";
$jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt='jxjy' LIMIT 1");
if (!$jk) {
    echo "❌ 易教育货源配置不存在\n";
    exit;
}
echo "✅ 易教育货源配置存在: {$jk['name']}\n";

// 2. 查询易教育订单
echo "2. 查询易教育订单\n";
$sql = "SELECT o.*, h.pt as platform_type 
        FROM qingka_wangke_order o 
        LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
        WHERE h.pt = 'jxjy' 
        AND o.dockstatus = 1 
        AND o.status NOT IN ('已完成', '已退款', '已取消')
        AND o.addtime > DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY o.addtime DESC 
        LIMIT 5";

$result = $DB->query($sql);
$sync_count = 0;
$error_count = 0;

if (!$result) {
    echo "❌ 数据库查询失败\n";
    exit;
}

$orders = [];
while ($order = $DB->fetch($result)) {
    $orders[] = $order;
}

if (empty($orders)) {
    echo "ℹ️ 没有找到需要同步的易教育订单\n";
} else {
    echo "✅ 找到 " . count($orders) . " 个需要同步的易教育订单\n";
    
    foreach ($orders as $order) {
        echo "处理订单: {$order['oid']} - {$order['user']} - {$order['status']} - {$order['process']}\n";
        
        try {
            // 调用进度查询函数
            $progress_result = processCx($order['oid']);
            
            if (!empty($progress_result)) {
                foreach ($progress_result as $item) {
                    if ($item['code'] == 1) {
                        echo "  ✅ 查询成功: 状态={$item['status_text']}, 进度={$item['process']}\n";
                        
                        // 更新订单状态
                        $update_sql = "UPDATE qingka_wangke_order SET 
                                      status = ?, 
                                      process = ?, 
                                      remarks = ?,
                                      yid = ?,
                                      uptime = NOW()
                                      WHERE oid = ?";
                        $update_result = $DB->prepare_query($update_sql, [
                            $item['status_text'],
                            $item['process'],
                            $item['remarks'],
                            $item['yid'],
                            $order['oid']
                        ]);
                        
                        if ($update_result) {
                            echo "  ✅ 数据库更新成功\n";
                            $sync_count++;
                        } else {
                            echo "  ❌ 数据库更新失败\n";
                            $error_count++;
                        }
                    } else {
                        echo "  ❌ 查询失败: {$item['msg']}\n";
                        $error_count++;
                    }
                }
            } else {
                echo "  ⚠️ processCx返回空结果\n";
                $error_count++;
            }
        } catch (Exception $e) {
            echo "  ❌ 异常: " . $e->getMessage() . "\n";
            $error_count++;
        }
        
        // 避免请求过于频繁
        usleep(500000); // 0.5秒延迟
    }
}

echo "\n同步完成，成功: {$sync_count}，失败: {$error_count}\n";

// 3. 测试API接口
echo "\n3. 测试API接口\n";
try {
    // 包含API文件并执行
    include('api/jxjyapi.php');
} catch (Exception $e) {
    echo "❌ API测试失败: " . $e->getMessage() . "\n";
}

echo "\n测试完成\n";
?>
