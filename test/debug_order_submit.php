<?php
/**
 * 调试订单提交问题
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>调试订单提交问题</h1>\n";
echo "<pre>\n";

// 模拟前端提交的数据
$test_data = array(
    'cid' => '1', // 易教育项目ID
    'data' => array(
        array(
            'userinfo' => 'test_user test_pass',
            'userName' => '测试用户',
            'data' => array(
                'id' => 'test_course_id',
                'name' => '测试课程',
                'kcjs' => '2025-12-31'
            )
        )
    )
);

echo "测试数据:\n";
echo "cid: {$test_data['cid']}\n";
echo "data: " . json_encode($test_data['data'], JSON_UNESCAPED_UNICODE) . "\n";

// 获取项目信息
$cid = $test_data['cid'];
$rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='$cid' LIMIT 1");

if (!$rs) {
    echo "❌ 项目不存在，cid: {$cid}\n";
    exit;
}

echo "\n项目信息:\n";
echo "  cid: {$rs['cid']}\n";
echo "  name: {$rs['name']}\n";
echo "  price: {$rs['price']}\n";
echo "  docking: {$rs['docking']}\n";
echo "  noun: {$rs['noun']}\n";

// 模拟用户信息
$userrow = array(
    'uid' => 1,
    'addprice' => 1,
    'money' => 1000
);

echo "\n用户信息:\n";
echo "  uid: {$userrow['uid']}\n";
echo "  addprice: {$userrow['addprice']}\n";
echo "  money: {$userrow['money']}\n";

// 计算价格
$danjia = round($rs['price'] * $userrow['addprice'], 2);
echo "\n价格计算:\n";
echo "  单价: {$danjia}\n";

// 处理订单数据
$data = $test_data['data'];
$clientip = '127.0.0.1';
$date = date('Y-m-d H:i:s');
$miaoshua = '0';

foreach ($data as $row) {
    echo "\n处理订单:\n";
    
    $userinfo = $row['userinfo'];
    $userName = $row['userName'];
    $userinfo = explode(" ", $userinfo);
    
    if (count($userinfo) > 2) {
        $school = $userinfo[0];
        $user = $userinfo[1];
        $pass = $userinfo[2];
    } else {
        $school = "自动识别";
        $user = $userinfo[0];
        $pass = $userinfo[1];
    }
    
    $kcid = $row['data']['id'];
    $kcname = $row['data']['name'];
    $kcjs = $row['data']['kcjs'];
    
    echo "  school: {$school}\n";
    echo "  user: {$user}\n";
    echo "  pass: {$pass}\n";
    echo "  kcid: {$kcid}\n";
    echo "  kcname: {$kcname}\n";
    echo "  kcjs: {$kcjs}\n";
    
    // 检查重复订单
    $duplicate_check = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE ptname='{$rs['name']}' AND school='$school' AND user='$user' AND pass='$pass' AND kcid='$kcid' AND kcname='$kcname'");
    
    if ($duplicate_check) {
        $dockstatus = '3'; // 重复下单
        echo "  ⚠️  检测到重复订单，dockstatus=3\n";
    } elseif ($rs['docking'] == 0) {
        $dockstatus = '99';
        echo "  ⚠️  项目未对接，dockstatus=99\n";
    } else {
        $dockstatus = '0';
        echo "  ✅ 正常订单，dockstatus=0\n";
    }
    
    // 构建SQL语句
    $sql = "INSERT INTO qingka_wangke_order (uid, cid, hid, ptname, school, name, user, pass, kcid, kcname, courseEndTime, fees, noun, miaoshua, addtime, ip, dockstatus) VALUES ('{$userrow['uid']}','{$rs['cid']}','{$rs['docking']}','{$rs['name']}','{$school}','$userName','$user','$pass','$kcid','$kcname','{$kcjs}','{$danjia}','{$rs['noun']}','$miaoshua','$date','$clientip','$dockstatus')";
    
    echo "\nSQL语句:\n";
    echo $sql . "\n";
    
    // 执行SQL
    echo "\n执行SQL...\n";
    $is = $DB->query($sql);
    
    if ($is) {
        echo "✅ 订单插入成功\n";
        
        // 获取插入的订单ID
        $oid = $DB->lastInsertId();
        echo "订单ID: {$oid}\n";
        
        // 更新用户余额（测试时不实际扣费）
        echo "✅ 模拟扣费成功\n";
        
    } else {
        echo "❌ 订单插入失败\n";
        
        // 获取数据库错误信息
        $error = $DB->error();
        echo "数据库错误: {$error}\n";
        
        // 检查字段是否存在
        echo "\n检查数据库表结构:\n";
        $columns = $DB->query("SHOW COLUMNS FROM qingka_wangke_order");
        $field_list = array();
        while ($col = $DB->fetch($columns)) {
            $field_list[] = $col['Field'];
        }
        echo "表字段: " . implode(', ', $field_list) . "\n";
        
        // 检查SQL中的字段是否都存在
        $sql_fields = array('uid', 'cid', 'hid', 'ptname', 'school', 'name', 'user', 'pass', 'kcid', 'kcname', 'courseEndTime', 'fees', 'noun', 'miaoshua', 'addtime', 'ip', 'dockstatus');
        $missing_fields = array_diff($sql_fields, $field_list);
        
        if (!empty($missing_fields)) {
            echo "❌ 缺少字段: " . implode(', ', $missing_fields) . "\n";
        } else {
            echo "✅ 所有字段都存在\n";
        }
    }
}

echo "\n=== 调试完成 ===\n";

echo "\n</pre>\n";
?>
