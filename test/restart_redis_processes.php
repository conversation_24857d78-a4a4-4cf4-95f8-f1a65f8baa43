<?php
/**
 * 重启Redis守护进程
 */

echo "<h1>重启Redis守护进程</h1>\n";
echo "<pre>\n";

echo "=== 检查当前进程状态 ===\n";

// 检查addchu.php进程
$addchu_processes = shell_exec("ps aux | grep addchu.php | grep -v grep");
if ($addchu_processes) {
    echo "当前运行的addchu.php进程:\n";
    echo $addchu_processes . "\n";
    
    // 获取进程ID
    $lines = explode("\n", trim($addchu_processes));
    $pids = array();
    foreach ($lines as $line) {
        if (trim($line)) {
            $parts = preg_split('/\s+/', trim($line));
            if (isset($parts[1])) {
                $pids[] = $parts[1];
            }
        }
    }
    
    if (!empty($pids)) {
        echo "\n杀死旧进程...\n";
        foreach ($pids as $pid) {
            echo "杀死进程 PID: {$pid}\n";
            shell_exec("kill -9 {$pid}");
        }
        echo "✅ 旧进程已杀死\n";
    }
} else {
    echo "没有找到运行中的addchu.php进程\n";
}

// 等待一下
sleep(2);

echo "\n=== 启动新进程 ===\n";

// 启动新的addchu.php进程
$redis_dir = dirname(__FILE__) . '/../redis';
$addchu_script = $redis_dir . '/addchu.php';

if (file_exists($addchu_script)) {
    echo "启动新的addchu.php进程...\n";
    
    // 使用nohup在后台启动
    $command = "cd {$redis_dir} && nohup php addchu.php > /dev/null 2>&1 &";
    shell_exec($command);
    
    // 等待一下让进程启动
    sleep(2);
    
    // 检查是否启动成功
    $new_processes = shell_exec("ps aux | grep addchu.php | grep -v grep");
    if ($new_processes) {
        echo "✅ 新进程启动成功:\n";
        echo $new_processes . "\n";
    } else {
        echo "❌ 新进程启动失败\n";
    }
} else {
    echo "❌ addchu.php文件不存在: {$addchu_script}\n";
}

echo "\n=== 测试修复效果 ===\n";

// 等待几秒让进程稳定
echo "等待进程稳定...\n";
sleep(5);

// 检查是否有待处理的订单
include_once(dirname(__FILE__) . '/../confing/common.php');

$pending_orders = $DB->get_row("SELECT COUNT(*) as count FROM qingka_wangke_order WHERE dockstatus=0");
echo "待处理订单数量: {$pending_orders['count']}\n";

if ($pending_orders['count'] > 0) {
    echo "✅ 有待处理订单，新进程应该会处理它们\n";
    echo "\n请观察进程日志，看是否还会出现'项目信息不存在'错误\n";
} else {
    echo "ℹ️ 没有待处理订单\n";
    echo "可以创建一个测试订单来验证修复效果\n";
}

echo "\n=== 监控建议 ===\n";
echo "1. 观察addchu.php的输出日志\n";
echo "2. 检查是否还有'项目信息不存在'错误\n";
echo "3. 确认订单能正常提交到上游\n";
echo "4. 监控订单状态变化（dockstatus: 0 → 1）\n";

echo "\n如果要手动监控进程输出，可以运行:\n";
echo "cd {$redis_dir} && php addchu.php\n";

echo "\n</pre>\n";
?>
