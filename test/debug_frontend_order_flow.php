<?php
/**
 * 调试前端下单的完整流程
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>调试前端下单的完整流程</h1>\n";
echo "<pre>\n";

// 获取最近失败的订单
echo "=== 查找最近失败的订单 ===\n";
$failed_orders = $DB->query("SELECT * FROM qingka_wangke_order WHERE hid=50 AND dockstatus IN (0,5) ORDER BY oid DESC LIMIT 5");

$target_order = null;
echo "最近的待处理订单:\n";
while ($row = $DB->fetch($failed_orders)) {
    echo "订单 {$row['oid']}: cid={$row['cid']}, ptname='{$row['ptname']}', dockstatus={$row['dockstatus']}\n";
    if (!$target_order) {
        $target_order = $row;
    }
}

if (!$target_order) {
    echo "❌ 没有找到待处理的订单\n";
    exit;
}

echo "\n分析订单: {$target_order['oid']}\n";

// 检查订单是通过哪个接口创建的
echo "\n=== 判断订单来源 ===\n";
if (strpos($target_order['ptname'], '易教育') !== false || strpos($target_order['ptname'], 'jxjy') !== false) {
    echo "订单来源: 易教育专用API (api/jxjyapi.php)\n";
    $order_source = 'jxjyapi';
} else {
    echo "订单来源: 主系统API (apisub.php)\n";
    $order_source = 'apisub';
}

// 检查订单的cid是否有效
echo "\n=== 检查订单cid有效性 ===\n";
$cid = $target_order['cid'];
echo "订单cid: {$cid}\n";

$class_info = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='{$cid}' LIMIT 1");
if ($class_info) {
    echo "✅ cid在商品表中存在\n";
    echo "  商品名称: {$class_info['name']}\n";
    echo "  商品docking: {$class_info['docking']}\n";
    echo "  商品noun: '{$class_info['noun']}'\n";
    echo "  商品状态: {$class_info['status']}\n";
    
    if ($class_info['docking'] == 50) {
        echo "✅ 商品属于易教育货源\n";
    } else {
        echo "❌ 商品不属于易教育货源，实际货源: {$class_info['docking']}\n";
    }
} else {
    echo "❌ cid在商品表中不存在\n";
    
    // 查找可能的原因
    echo "\n查找可能的原因:\n";
    
    // 检查是否有相似的cid
    $similar_cids = $DB->query("SELECT cid,name,docking FROM qingka_wangke_class WHERE cid LIKE '%{$cid}%' OR cid LIKE '{$cid}%' LIMIT 5");
    $found_similar = false;
    while ($similar = $DB->fetch($similar_cids)) {
        if (!$found_similar) {
            echo "  相似的cid:\n";
            $found_similar = true;
        }
        echo "    cid={$similar['cid']}: {$similar['name']} (docking={$similar['docking']})\n";
    }
    
    if (!$found_similar) {
        echo "  没有找到相似的cid\n";
    }
}

// 如果是易教育API下单，模拟下单过程
if ($order_source == 'jxjyapi') {
    echo "\n=== 模拟易教育API下单过程 ===\n";
    
    // 模拟前端传递的参数
    $test_id = $cid;
    echo "前端传递的id: {$test_id}\n";
    
    // 步骤1: 查询项目信息
    echo "\n步骤1: 查询项目信息\n";
    echo "  SQL: SELECT * FROM qingka_wangke_class WHERE cid='{$test_id}'\n";
    $rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='{$test_id}' LIMIT 1");
    
    if ($rs) {
        echo "  ✅ 项目查询成功\n";
        echo "    项目名称: {$rs['name']}\n";
        echo "    项目docking: {$rs['docking']}\n";
        echo "    项目noun: '{$rs['noun']}'\n";
        
        // 步骤2: 检查货源
        echo "\n步骤2: 检查货源配置\n";
        $jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt='jxjy' LIMIT 1");
        if ($jk) {
            echo "  ✅ 易教育货源存在，hid: {$jk['hid']}\n";
            
            if ($rs['docking'] == $jk['hid']) {
                echo "  ✅ 项目属于易教育货源\n";
                
                // 步骤3: 模拟订单创建
                echo "\n步骤3: 模拟订单创建\n";
                $number = $rs['noun'];
                echo "  使用的项目编号(noun): '{$number}'\n";
                
                echo "  订单创建SQL:\n";
                echo "    INSERT INTO qingka_wangke_order (uid,cid,hid,yid,ptname,school,user,pass,kcname,noun,status,process,addtime,dockstatus)\n";
                echo "    VALUES (uid,'{$test_id}','{$jk['hid']}',yid,'{$rs['name']}','',user,pass,'','{$number}','已提交','0%',now,'0')\n";
                
                echo "  ✅ 订单创建逻辑正确\n";
                
            } else {
                echo "  ❌ 项目不属于易教育货源\n";
                echo "    项目docking: {$rs['docking']}\n";
                echo "    易教育hid: {$jk['hid']}\n";
            }
        } else {
            echo "  ❌ 易教育货源不存在\n";
        }
        
    } else {
        echo "  ❌ 项目查询失败\n";
        echo "  这就是问题所在！前端传递的cid无效\n";
    }
}

// 检查addWk函数的执行
echo "\n=== 检查addWk函数执行 ===\n";
echo "当addWk函数处理订单 {$target_order['oid']} 时:\n";

echo "\n1. 获取订单信息:\n";
echo "   cid: {$target_order['cid']}\n";
echo "   noun: '{$target_order['noun']}'\n";

echo "\n2. 查询商品信息:\n";
echo "   SQL: SELECT * FROM qingka_wangke_class WHERE cid='{$target_order['cid']}'\n";
$b = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='{$target_order['cid']}' LIMIT 1");

if ($b) {
    echo "   ✅ 商品查询成功\n";
    echo "   docking: {$b['docking']}\n";
    
    echo "\n3. 查询货源信息:\n";
    $hid = $b['docking'];
    echo "   SQL: SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$hid}'\n";
    $a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$hid}' LIMIT 1");
    
    if ($a) {
        echo "   ✅ 货源查询成功\n";
        echo "   货源类型: {$a['pt']}\n";
        
        if ($a['pt'] == 'jxjy') {
            echo "\n4. 易教育项目信息查询:\n";
            echo "   SQL: SELECT * FROM qingka_wangke_class WHERE cid = '{$target_order['cid']}'\n";
            $class_info_check = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = '{$target_order['cid']}' LIMIT 1");
            
            if ($class_info_check) {
                echo "   ✅ 项目信息查询成功（修复后的逻辑）\n";
                echo "   这个订单应该能正常处理\n";
            } else {
                echo "   ❌ 项目信息查询失败（修复后的逻辑）\n";
                echo "   问题确认：cid无效\n";
            }
        } else {
            echo "   ❌ 不是易教育项目\n";
        }
    } else {
        echo "   ❌ 货源查询失败\n";
    }
} else {
    echo "   ❌ 商品查询失败\n";
    echo "   问题确认：订单的cid在商品表中不存在\n";
}

// 解决方案
echo "\n=== 问题诊断与解决方案 ===\n";

if (!$class_info) {
    echo "❌ 问题确认：前端下单时创建的订单cid无效\n";
    
    echo "\n可能的原因:\n";
    echo "1. 前端传递了错误的项目ID\n";
    echo "2. 商品表数据被删除或损坏\n";
    echo "3. 前端与后端的项目ID不同步\n";
    
    echo "\n解决方案:\n";
    echo "1. 检查前端选择项目时的ID来源\n";
    echo "2. 确保商品表数据完整性\n";
    echo "3. 在下单前添加数据验证\n";
    echo "4. 修复或重新创建缺失的商品数据\n";
    
    // 提供修复建议
    echo "\n修复建议:\n";
    echo "如果确认是商品数据缺失，可以:\n";
    echo "1. 从备份恢复商品数据\n";
    echo "2. 重新创建易教育项目\n";
    echo "3. 检查数据库完整性\n";
    
} else {
    echo "✅ 数据结构正常\n";
    echo "问题可能在其他地方，需要进一步调试\n";
}

echo "\n</pre>\n";
?>
