<?php
/**
 * 修复易教育订单进度显示问题
 * 直接修复状态为"1"、进度为"1"的异常订单
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始修复易教育订单进度问题...\n";

// 包含数据库配置
include('confing/common.php');

if (!$DB) {
    echo "❌ 数据库连接失败\n";
    exit;
}

echo "✅ 数据库连接成功\n";

// 查找有问题的易教育订单
$sql = "SELECT o.oid, o.user, o.status, o.process, o.yid, h.pt, h.name as platform_name
        FROM qingka_wangke_order o 
        LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
        WHERE h.pt = 'jxjy' 
        AND (o.status = '1' OR o.process = '1')
        ORDER BY o.addtime DESC";

$result = $DB->query($sql);
if (!$result) {
    echo "❌ 数据库查询失败\n";
    exit;
}

$problem_orders = [];
while ($order = $DB->fetch($result)) {
    $problem_orders[] = $order;
}

if (empty($problem_orders)) {
    echo "✅ 没有发现有问题的易教育订单\n";
} else {
    echo "🔍 发现 " . count($problem_orders) . " 个有问题的易教育订单:\n";
    
    foreach ($problem_orders as $order) {
        echo "  订单ID: {$order['oid']}, 用户: {$order['user']}, 状态: {$order['status']}, 进度: {$order['process']}\n";
    }
    
    echo "\n开始修复这些订单...\n";
    
    $fixed_count = 0;
    foreach ($problem_orders as $order) {
        echo "修复订单 {$order['oid']}...\n";
        
        // 根据订单的实际情况设置合理的状态
        $new_status = '队列中';  // 默认状态
        $new_process = '0%';     // 默认进度
        
        // 如果有yid且不为空，说明已经提交到上游，设置为进行中
        if (!empty($order['yid']) && $order['yid'] != '1') {
            $new_status = '进行中';
            $new_process = '10%';
        }
        
        // 更新订单状态
        $update_sql = "UPDATE qingka_wangke_order SET 
                       status = ?, 
                       process = ?, 
                       remarks = '系统自动修复异常状态',
                       uptime = NOW()
                       WHERE oid = ?";
        
        $update_result = $DB->prepare_query($update_sql, [
            $new_status,
            $new_process,
            $order['oid']
        ]);
        
        if ($update_result) {
            echo "  ✅ 订单 {$order['oid']} 修复成功: {$new_status} - {$new_process}\n";
            $fixed_count++;
        } else {
            echo "  ❌ 订单 {$order['oid']} 修复失败\n";
        }
    }
    
    echo "\n修复完成，成功修复 {$fixed_count} 个订单\n";
}

// 检查易教育货源配置
echo "\n检查易教育货源配置...\n";
$jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt='jxjy' LIMIT 1");
if ($jk) {
    echo "✅ 易教育货源配置存在:\n";
    echo "  货源ID: {$jk['hid']}\n";
    echo "  货源名称: {$jk['name']}\n";
    echo "  API地址: {$jk['url']}\n";
    echo "  Token状态: " . (empty($jk['token']) ? "❌ 未设置" : "✅ 已设置") . "\n";
} else {
    echo "❌ 易教育货源配置不存在\n";
}

// 检查订单提交状态
echo "\n检查易教育订单提交状态...\n";
$status_sql = "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN dockstatus = 0 THEN 1 ELSE 0 END) as pending_submit,
                SUM(CASE WHEN dockstatus = 1 THEN 1 ELSE 0 END) as submitted,
                SUM(CASE WHEN dockstatus = 2 THEN 1 ELSE 0 END) as failed
               FROM qingka_wangke_order o 
               LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
               WHERE h.pt = 'jxjy'
               AND o.addtime > DATE_SUB(NOW(), INTERVAL 7 DAY)";

$status_result = $DB->get_row($status_sql);
if ($status_result) {
    echo "  总订单数: {$status_result['total']}\n";
    echo "  待提交: {$status_result['pending_submit']}\n";
    echo "  已提交: {$status_result['submitted']}\n";
    echo "  提交失败: {$status_result['failed']}\n";
}

echo "\n🎉 易教育订单修复完成！\n";
echo "\n建议:\n";
echo "1. 设置计划任务定期运行进度同步: */3 * * * * php /path/to/api/jxjyapi.php?action=sync_orders\n";
echo "2. 监控订单提交队列，确保 addru.php 和 addchu.php 正常运行\n";
echo "3. 检查易教育API的Token是否有效\n";

?>
