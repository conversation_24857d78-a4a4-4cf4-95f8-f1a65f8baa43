<?php
include('../confing/common.php');
include('../ayconfig.php');
$hid = '';
$jk = $DB->get_row("select * from qingka_wangke_huoyuan where hid=$hid ");
$header = ["Content-Type: application/json; charset=utf-8", "Authorization: Bearer " . $jk['token']];
$now_time = date("Y-m-d H:i:s");
switch ($act) {
    case 'jxjyclass':
        $a = $DB->query("select * from qingka_wangke_jxjyclass where status=1 order by id asc");
        $data = array();
        while ($row = $DB->fetch($a)) {
            $price = $row['price'] * $userrow['addprice'];
            $formattedPrice = number_format($price, 2);
            $row['price'] = $formattedPrice;
            $data[] = $row;
        }
        $response = array('code' => 1, 'data' => $data);
        exit(json_encode($response));
        break;
    case 'getcourse':
        $id = trim(strip_tags(daddslashes($_POST['id'])));
        $username = trim(strip_tags(daddslashes($_POST['user'])));
        $password = trim(strip_tags(daddslashes($_POST['pass'])));
        $rs = $DB->get_row("select * from qingka_wangke_jxjyclass where id='$id' limit 1 ");
        $number = $rs['number'];
        wlog($userrow['uid'], "继续教育查课", "平台{$rs['name']} {$username} {$password}", 0);
        if ($rs['isSearchCourse'] == '0') {
            jsonReturn(-1, "该项目无需查课");
        }
        $data = array(
            "websiteNumber" => $number,
            "data" => array(array("username" => $username, "password" => $password))
        );
        $data = json_encode($data);
        $jxjy_url = $jk["url"] . "/api/website/queryCourse";
        $result = post($jxjy_url, $data, $header);
        $result = json_decode($result, true);
        if ($result['code'] == 200) {
            $uuid = $result['data']['uuid'];
            // 延时5秒
            sleep(5);
            $data2 = array(
                "uuid" => $uuid,
            );
            $data2 = json_encode($data2);
            $jxjy_url2 = $jk["url"] . "/api/website/getQueryCourse";
            $result2 = post($jxjy_url2, $data2, $header);
            $result2 = json_decode($result2, true);
            if ($result2['code'] == 200 && strpos($result2['data'][0]['name'], "登录失败") === false) {
                if (isset($result2['data'][0]['children'])) {
                    $children = $result2['data'][0]['children'];
                    $filteredData = array();
                    foreach ($children as $child) {
                        if (isset($child['id']) && isset($child['name'])) {
                            $filteredData[] = array("id" => $child['id'], "name" => $child['name']);
                        } elseif (isset($child['name']) && isset($child['children'])) {
                            foreach ($child['children'] as $subChild) {
                                if (isset($subChild['id']) && isset($subChild['name'])) {
                                    $filteredData[] = array("id" => $subChild['id'], "name" => $child['name'] . "----" . $subChil);
                                }
                            }
                        }
                    }
                    $responseData = array(array("code" => 1, "data" => $filteredData, "msg" => "查询成功", "userinfo" => $result2['data'][0]['name']));
                    exit(json_encode($responseData));
                }
            } else {
                $responseData = array(array("code" => -1, "msg" => $result2['data'][0]['name']));
                exit(json_encode($responseData));
            }
        } else {
            $responseData = array(array("code" => -1, "msg" => '发送查课请求失败，请联系管理员'));
            exit(json_encode($responseData));
        }
        break;

    case 'jxjyadd':
        $id = trim(strip_tags(daddslashes($_POST['id'])));
        $username = trim(strip_tags(daddslashes($_POST['user'])));
        $password = trim(strip_tags(daddslashes($_POST['pass'])));
        $select = $_POST['select'];
        $rs = $DB->get_row("select * from qingka_wangke_jxjyclass where id='$id' limit 1 ");
        $number = $rs['number'];
        if ($id == '') {
            jsonReturn(-1, "ID不能为空");
        }
        if ($username == '') {
            jsonReturn(-1, "账号不能为空");
        }
        if ($password == '') {
            jsonReturn(-1, "密码不能为空");
        }
        if ($rs['must'] == '0') {
            $danjia = round($rs['price'] * $userrow['addprice'], 2);
            if ($userrow['money'] < $danjia) {
                jsonReturn(-1, "您的余额不足");
            }
            if ($danjia == 0 || $userrow['addprice'] < 0.1) {
                exit('{"code":-1,"msg":"大佬，我得罪不起您，我小本生意，有哪里得罪之处，还望多多包涵"}');
            }
            $data = array("websiteNumber" => $number, "data" => array(array("username" => $username, "password" => $password,)));
            $data = json_encode($data);
            $jxjy_url = $jk["url"] . "/api/order/buy";
            $result = post($jxjy_url, $data, $header);
            $result = json_decode($result, true);
            if ($result['code'] == 200) {
                $is = $DB->query("insert into qingka_wangke_jxjyorder (uid,cid,yid,ptname,name,user,pass,kcid,kcname,fees,noun,status,ip,addtime) values ('{$userrow['uid']}','{$id}','','{$rs['name']}','','$username','$password','','','{$danjia}','{$rs['number']}','待更新','$clientip','$now_time') "); //将对应课程写入数据库	               	       	      	
                if ($is) {
                    $DB->query("update qingka_wangke_user set money=money-'{$danjia}' where uid='{$userrow['uid']}' limit 1 ");
                    wlog($userrow['uid'], "继续教育下单", "平台{$rs['name']} {$username} {$password}扣除{$danjian}元", -$danjia);
                    jsonReturn(1, "成功下单");
                } else {
                    jsonReturn(-1, "未知错误");
                }
            } else {
                jsonReturn(-1, $result['data'][0]['message']);
            }
        } else { //查课下单
            if ($select == '') {
                jsonReturn(-1, "请选择课程");
            }
            $success = 0;
            foreach ($select as $item) {
                $kcid = $item['id'];
                $kcname = $item['name'];
                $danjia = round($rs['price'] * $userrow['addprice'], 2);
                if ($userrow['money'] < $danjia) {
                    jsonReturn(-1, "您的余额不足");
                }
                if ($danjia == 0 || $userrow['addprice'] < 0.1) {
                    exit('{"code":-1,"msg":"大佬，我得罪不起您，我小本生意，有哪里得罪之处，还望多多包涵"}');
                }
                if (strpos($kcname, '----') !== false) {
                    list($kcname1, $kcname2) = explode('----', $kcname);
                    $data = array("websiteNumber" => $number, "data" => array(array("username" => $username, "password" => $password, "name" => $username . "----" . $password,  "children" => array(array("name" => $kcname1, "children" => array(array("name" => $kcname2, "disabled" => false, "id" => $kcid, "selected" => true)), "disabled" => true, "selected" => true)), "selected" => true)));
                } else {
                    $data = array(
                        "websiteNumber" => $number, "data" => array(array("username" => $username, "password" => $password, "name" => $username . "----" . $password,          "children" => array(array("name" => $kcname, "disabled" => false, "id" => $kcid, "selected" => true)),        "selected" => true))
                    );
                }
                $data = json_encode($data);
                $jxjy_url = $jk["url"] . "/api/order/buy";
                $result = post($jxjy_url, $data, $header);
                $result = json_decode($result, true);
                if ($result['code'] == 200) {
                    $is = $DB->query("insert into qingka_wangke_jxjyorder (uid,cid,yid,ptname,name,user,pass,kcid,kcname,fees,noun,status,ip,addtime) values ('{$userrow['uid']}','{$id}','','{$rs['name']}','','$username','$password','$kcid','$kcname','{$danjia}','{$rs['number']}','','$clientip','$now_time') "); //将对应课程写入数据库	               	       	      	
                    if ($is) {
                        $DB->query("update qingka_wangke_user set money=money-'{$danjia}' where uid='{$userrow['uid']}' limit 1 ");
                        wlog($userrow['uid'], "继续教育下单", "平台{$rs['name']} {$username} {$password} {$kcname}扣除{$danjian}元", -$danjia);
                        $success++;
                    }
                } else {
                    jsonReturn(-1, "未知错误");
                }
            }
            if ($is) {
                exit('{"code":1,"msg":"提交成功，共提交' . $success . '条"}');
            } else {
                exit('{"code":-1,"msg":"提交失败"}');
            }
        }
        break;
    case 'jxjyorderlist':
        $cx = daddslashes($_POST['cx']);
        $page = trim(strip_tags(daddslashes($_POST['page'])));
        $pagesize = trim(strip_tags($cx['pagesize']));;
        if ($pagesize == "") {
            $pagesize = 20;
        }
        $pageu = ($page - 1) * $pagesize; //当前界面		
        $qq = trim(strip_tags($cx['phone']));
        $status_text = trim(strip_tags($cx['status_text']));
        $dock = trim(strip_tags($cx['dock']));
        $oid = trim(strip_tags($cx['oid']));
        $uid = trim(strip_tags($cx['uid']));
        $serialNumber = trim(strip_tags($cx['serialNumber']));
        $status_text1 = trim(strip_tags($cx['status_text1']));
        if ($userrow['uid'] != '1') {
            $sql1 = "where uid='{$userrow['uid']}'";
        } else {
            $sql1 = "where 1=1";
        }
        if ($qq != '') {
            $sql3 = " and user='{$qq}'";
        }
        if ($oid != '') {
            $sql4 = " and oid='{$oid}'";
        }
        if ($uid != '') {
            $sql5 = " and uid='{$uid}'";
        }
        if ($status_text != '') {
            $sql6 = " and status='{$status_text}'";
        }
        if ($dock != '') {
            $sql7 = " and dockstatus='{$dock}'";
        }
        if ($status_text1 != '') {
            $sql8 = " and status='{$status_text1}'";
        }
        $sql = $sql1 . $sql2 . $sql3 . $sql4 . $sql5 . $sql6 . $sql7 . $sql8;
        $a = $DB->query("select * from qingka_wangke_jxjyorder {$sql} order by oid desc limit $pageu,$pagesize ");
        $count1 = $DB->count("select count(*) from qingka_wangke_jxjyorder {$sql} ");
        $i = 0;
        while ($row = $DB->fetch($a)) {
            if ($row['name'] == '' || $row['name'] == 'undefined') {
                $row['name'] = 'null';
            }
            $i = $i + 1;
            $data[] = $row;
        }
        $last_page = ceil($count1 / $pagesize); //取最大页数
        $data = array('code' => 1, 'data' => $data, "current_page" => (int)$page, "last_page" => $last_page, 'i' => $i, "uid" => (int)$userrow['uid']);
        exit(json_encode($data));
        break;
    case 'jxjyuporder':
        $oid = trim(strip_tags(daddslashes($_GET['oid'])));
        $row = $DB->get_row("select * from qingka_wangke_order where oid='$oid'");
        $result = jxjyprocess($oid);
        if ($result['code'] === 1) {
            exit(json_encode(['code' => 1, 'msg' => '同步成功']));
        } else {
            exit(json_encode(['code' => -1, 'msg' => '同步失败']));
        }
        break;
    case 'jxjxgmbs':
        $oid = trim(strip_tags(daddslashes($_GET['oid'])));
        $newpass = trim(strip_tags(daddslashes($_POST['newpass'])));
        $row = $DB->get_row("SELECT * FROM qingka_wangke_jxjyorder WHERE oid='$oid'");
        if ($row['yid'] == '') {
            jsonReturn(-1, "请先同步后再补刷");
        }
        if ($newpass == "") {
            jsonReturn(-1, "新密码不能为空");
        } elseif ($row['uid'] == $userrow['uid']) {
            $data = array("number" => $row['yid'], "username" => $row['user'], "password" => $newpass,);
            $data = json_encode($data);
            $jxjy_url = $jk["url"] . "/api/order/edit";
            $result = post($jxjy_url, $data, $header);
            $result = json_decode($result, true);
            if ($result['code'] == 200) {
                $DB->get_row("update qingka_wangke_jxjyorder set pass='{$newpass}',status = '待更新' where oid='{$oid}' ");
                // wlog($userrow['uid'], "改密补刷", "改密成功", 0);
                jsonReturn(1, '补刷成功  新密码 ' . $newpass, ' 修改成功');
            } else {
                jsonReturn(-1, $result["message"]);
            }
        } else {
            jsonReturn(-1, "该订单不是你的");
        }
        break;
}
function jxjyprocess($oid)
{
    $hid = '';
    global $DB;
    $d = $DB->get_row("select * from qingka_wangke_jxjyorder where oid='{$oid}' ");
    $jk = $DB->get_row("select * from qingka_wangke_huoyuan where hid=$hid ");
    $class = $DB->get_row("select * from qingka_wangke_jxjyclass where id='{$d["cid"]}' ");
    $isSearchCourse = $class['isSearchCourse'];
    $url = $jk["url"];
    $token = $jk["token"];
    $header = ["Content-Type: application/json; charset=utf-8", "Authorization: Bearer " . $token];
    $username = $d["user"];
    $kcname = $d["kcname"];
    $orderNumber = $d["yid"];
    $websiteNumber = $d["noun"];
    $data = array(
        "pageSize" => 10, "pageNum" => 1, "status" => "all", "websiteNumber" => $websiteNumber, "orderNumber" => $orderNumber, "username" => $username, "name" => '',
    );
    $data = json_encode($data);
    $jxjy_url = $url . "/api/order/list";
    $result = post($jxjy_url, $data, $header);
    $result = json_decode($result, true);
    if ($result['code'] === 200) {
        $list = $result['data']['list'];
        $status_mapping = [
            0 => '队列中', 1 => '完成', 2 => '运行中', 3 => '异常', 4 => '正在售后', 5 => '售后完成', 6 => '人工处理', 7 => '已退款', 8 => '暂停中', 9 => '已暂停'
        ];
        foreach ($list as $item) {
            $status_text = $status_mapping[$item['status']];
            if ($orderNumber == '' &&  $isSearchCourse == '0') {
                $DB->query("UPDATE qingka_wangke_jxjyorder SET `name` = '{$item['name']}', `yid` = '{$item['number']}', `status` = '{$status_text}', `process` = '{$item['progress']}' WHERE `user` = '{$item['username']}' AND `oid` = '{$oid}'");
                return ['code' => 1, 'msg' => '同步成功'];
            } elseif ($orderNumber != '' &&  $isSearchCourse == '0') {
                $DB->query("UPDATE qingka_wangke_jxjyorder SET `name` = '{$item['name']}', `status` = '{$status_text}', `process` = '{$item['progress']}' WHERE `user` = '{$item['username']}' AND `oid` = '{$oid}'");
                return ['code' => 1, 'msg' => '同步成功'];
            }
            $courses = [];
            foreach ($item['courses'] as $course) {
                $course_name = $course['name'];
                if (isset($course['children'])) {
                    $children_names = array_map(function ($child) {
                        return $child['name'];
                    }, $course['children']);
                    $course_name .= '----' . implode('----', $children_names);
                }
                $courses[] = [
                    'name' => $course_name, 'id' => $course['id']
                ];
                if ($kcname == $course_name) {
                    if ($orderNumber == '' && $isSearchCourse == '1') {
                        $DB->query("UPDATE qingka_wangke_jxjyorder SET `name` = '{$item['name']}', `yid` = '{$item['number']}', `status` = '{$status_text}', `process` = '{$item['progress']}' WHERE `user` = '{$item['username']}' AND `kcname` = '{$course_name}' AND `oid` = '{$oid}'");
                        return ['code' => 1, 'msg' => '同步成功'];
                    } elseif ($orderNumber != '' && $isSearchCourse == '1') {
                        $DB->query("UPDATE qingka_wangke_jxjyorder SET `name` = '{$item['name']}', `status` = '{$status_text}', `process` = '{$item['progress']}' WHERE `user` = '{$item['username']}' AND `oid` = '{$oid}'");
                        return ['code' => 1, 'msg' => '同步成功'];
                    } else {
                        $DB->query("UPDATE qingka_wangke_jxjyorder SET `name` = '{$item['name']}', `status` = '{$status_text}', `process` = '{$item['progress']}' WHERE `user` = '{$item['username']}' AND `kcname` = '{$course_name}' AND `oid` = '{$oid}'");
                        return ['code' => 1, 'msg' => '同步成功'];
                    }
                }
            }
        }
    } else {
        return ['code' => -1, 'msg' => '同步失败'];
    }
}
