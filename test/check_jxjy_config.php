<?php
/**
 * 检查易教育项目配置
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育项目配置检查</h1>\n";
echo "<pre>\n";

// 获取易教育货源配置
$jxjy_huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
if (!$jxjy_huoyuan) {
    echo "❌ 未找到易教育货源配置\n";
    exit;
}

echo "✅ 易教育货源配置: ID={$jxjy_huoyuan['hid']}, 名称={$jxjy_huoyuan['name']}\n\n";

// 检查易教育项目的详细配置
echo "=== 易教育项目详细配置 ===\n";
$projects = $DB->query("SELECT cid,name,queryplat,getnoun,noun,docking,status FROM qingka_wangke_class WHERE docking='{$jxjy_huoyuan['hid']}' ORDER BY cid ASC LIMIT 5");

while ($row = $DB->fetch($projects)) {
    echo "项目 {$row['cid']}: {$row['name']}\n";
    echo "  状态: " . ($row['status'] == 1 ? '启用' : '禁用') . "\n";
    echo "  docking: {$row['docking']} " . ($row['docking'] == $jxjy_huoyuan['hid'] ? '✅' : '❌') . "\n";
    echo "  queryplat: '{$row['queryplat']}' " . (empty($row['queryplat']) ? '❌ 为空' : '✅') . "\n";
    echo "  getnoun: '{$row['getnoun']}'\n";
    echo "  noun: '{$row['noun']}'\n";
    
    // 检查配置是否正确
    $issues = array();
    if (empty($row['queryplat'])) {
        $issues[] = "queryplat字段为空";
    }
    if (empty($row['noun'])) {
        $issues[] = "noun字段为空";
    }
    if ($row['docking'] != $jxjy_huoyuan['hid']) {
        $issues[] = "docking字段不正确";
    }
    
    if (empty($issues)) {
        echo "  ✅ 配置正确\n";
    } else {
        echo "  ❌ 配置问题: " . implode(', ', $issues) . "\n";
    }
    echo "\n";
}

// 修复建议
echo "=== 修复建议 ===\n";
echo "问题分析:\n";
echo "1. queryplat字段为空，导致查课时无法找到正确的货源\n";
echo "2. queryplat字段应该指向易教育货源ID: {$jxjy_huoyuan['hid']}\n";

echo "\n修复方案:\n";
echo "执行以下SQL语句修复易教育项目的queryplat字段:\n";
echo "UPDATE qingka_wangke_class SET queryplat = '{$jxjy_huoyuan['hid']}' WHERE docking = '{$jxjy_huoyuan['hid']}' AND (queryplat = '' OR queryplat IS NULL);\n";

// 自动修复选项
if (isset($_GET['fix']) && $_GET['fix'] == '1') {
    echo "\n=== 自动修复 ===\n";
    $fix_sql = "UPDATE qingka_wangke_class SET queryplat = '{$jxjy_huoyuan['hid']}' WHERE docking = '{$jxjy_huoyuan['hid']}' AND (queryplat = '' OR queryplat IS NULL)";
    $result = $DB->query($fix_sql);
    
    if ($result) {
        $affected_rows = $DB->affected_rows();
        echo "✅ 修复成功，更新了 {$affected_rows} 个项目的queryplat字段\n";
        
        // 重新检查
        echo "\n修复后的项目配置:\n";
        $projects = $DB->query("SELECT cid,name,queryplat,noun FROM qingka_wangke_class WHERE docking='{$jxjy_huoyuan['hid']}' ORDER BY cid ASC LIMIT 3");
        while ($row = $DB->fetch($projects)) {
            echo "项目 {$row['cid']}: queryplat='{$row['queryplat']}', noun='{$row['noun']}'\n";
        }
    } else {
        echo "❌ 修复失败\n";
    }
} else {
    echo "\n如果要自动修复，请访问: " . $_SERVER['REQUEST_URI'] . "?fix=1\n";
}

echo "\n</pre>\n";
?>
