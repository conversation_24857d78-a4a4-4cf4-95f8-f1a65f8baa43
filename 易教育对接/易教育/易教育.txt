
登陆：
请求 URL
http:// **************:9900/api/login
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-origin
*
cache-control
no-cache, private
connection
keep-alive
content-encoding
gzip
content-type
text/html; charset=UTF-8
date
Fri, 20 Jun 2025 08:38:43 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding
accept
application/json
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
content-length
50
content-type
application/json
host
 **************:9900
origin
http://*************:9988
proxy-connection
keep-alive
referer
http://*************:9988/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
负载：{username: "573749877", password: "liuyaxin123."}
password
: 
"liuyaxin123."
username
: 
"573749877"
返回：{code: 200, status: "success",…}
code
: 
200
data
: 
{message: "登录成功", token: "62GkwcYWHjzCx0m07FcWJvV5WHohrkDjuBJsHeMr"}
status
: 
"success"
登录之后还会有几个api：
余额：
请求 URL
http:// **************:9900/api/user/info
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-origin
*
cache-control
no-cache, private
connection
keep-alive
content-encoding
gzip
content-type
text/html; charset=UTF-8
date
Sat, 21 Jun 2025 16:39:30 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding
accept
application/json
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer d6Nw1CeIqqvLu75BBKC21lCAkUA5ibP7AYDjrYOa
content-type
application/json
host
 **************:9900
origin
http://*************:9988
proxy-connection
keep-alive
referer
http://*************:9988/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
返回：
{code: 200, status: "success", data: {userName: "573749877", balance: "32.50", discount: "1.0"}}
code
: 
200
data
: 
{userName: "573749877", balance: "32.50", discount: "1.0"}
balance
: 
"32.50"
discount
: 
"1.0"
userName
: 
"573749877"
status
: 
"success"
其中balance就是这个网站的余额

公告api：
请求 URL
http:// **************:9900/api/notice/list
请求方法
GET
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-origin
*
cache-control
no-cache, private
connection
keep-alive
content-encoding
gzip
content-type
text/html; charset=UTF-8
date
Sat, 21 Jun 2025 16:39:30 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding
accept
application/json
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer d6Nw1CeIqqvLu75BBKC21lCAkUA5ibP7AYDjrYOa
content-type
application/json
host
 **************:9900
origin
http://*************:9988
proxy-connection
keep-alive
referer
http://*************:9988/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
返回：
{code: 200, status: "success",…}
code
: 
200
data
: 
[{title: "关于新增项目上线公告", content: "新增：华医网-掌上华医app-基层培训【年赛争峰考试】", publishDate: "2025-06-18"},…]
0
: 
{title: "关于新增项目上线公告", content: "新增：华医网-掌上华医app-基层培训【年赛争峰考试】", publishDate: "2025-06-18"}
1
: 
{title: "关于平台“手机版”上线公告", content: "手机版地址：http://*************:9966/login", publishDate: "2025-06-01"}
2
: 
{title: "关于“易教育”平台补贴第二轮活动",…}
3
: 
{title: "关于“华医网”全系支持申请证书统一考试", content: "目前“华医网”支持申请证书统一考试，包含公选课、各类课程与各类专项。",…}
4
: 
{title: "关于“易教育”平台补贴第一轮活动", content: "自2025-05-09 00:00:00起，全平台全项目5折优惠，截止日期2025-06-01 00:00:00",…}
status
: 
"success"

下单api：
请求 URL
http:// **************:9900/api/website/list
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-origin
*
cache-control
no-cache, private
connection
keep-alive
content-encoding
gzip
content-type
text/html; charset=UTF-8
date
Sat, 21 Jun 2025 16:43:21 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding
负载：{}
返回：
{code: 200, status: "success",…}
code
: 
200
data
: 
[{id: 1, number: 10010101, search: "国培网-融学Web-https://web.chinahrt.com", name: "国培网-融学Web",…},…]
[0 … 99]
0
: 
{id: 1, number: 10010101, search: "国培网-融学Web-https://web.chinahrt.com", name: "国培网-融学Web",…}
date
: 
"2023-07-26 10:00:00"
description
: 
"大概1至2小时完成"
format
: 
"账号 密码"
id
: 
1
isExam
: 
1
isSearchCourse
: 
1
must
: 
1
name
: 
"国培网-融学Web"
number
: 
10010101
price
: 
0.3
rate
: 
1
remark
: 
"无"
search
: 
"国培网-融学Web-https://web.chinahrt.com"
status
: 
1
unit
: 
"年度"
unitId
: 
1
url
: 
"https://web.chinahrt.com"。。。。。以下省略，会出现所有可以学习的网站和详细说明，可以搜索自己想学习名字，然后选择后会出现详细说明

还有出现一个输入框，输入想学习的账号密码或者按照要求填写，然后有一个查课按钮
api：
请求 URL
http:// **************:9900/api/website/queryCourse
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-origin
*
cache-control
no-cache, private
connection
keep-alive
content-encoding
gzip
content-type
text/html; charset=UTF-8
date
Sat, 21 Jun 2025 16:46:36 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding
accept
application/json
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer d6Nw1CeIqqvLu75BBKC21lCAkUA5ibP7AYDjrYOa
content-length
91
content-type
application/json
host
 **************:9900
origin
http://*************:9988
proxy-connection
keep-alive
referer
http://*************:9988/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
负载：
{websiteNumber: 10330101, data: [{username: "440883199502182914", password: "aA123456"}]}
data
: 
[{username: "440883199502182914", password: "aA123456"}]
0
: 
{username: "440883199502182914", password: "aA123456"}
websiteNumber
: 
10330101
其中440883199502182914和aA123456是我示例的账号密码，
发送查课成功以后，会返回一个查到的课程信息的api：
请求 URL
http:// **************:9900/api/website/getQueryCourse
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-origin
*
cache-control
no-cache, private
connection
keep-alive
content-encoding
gzip
content-type
text/html; charset=UTF-8
date
Sat, 21 Jun 2025 16:46:40 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding
负载:{uuid: "a19ba30f-86a1-4556-992e-9c6c0e357419"}
uuid
: 
"a19ba30f-86a1-4556-992e-9c6c0e357419"
返回查课结果：
{code: 200, status: "success",…}
code
: 
200
data
: 
[{username: "440883199502182914", password: "aA123456", name: "440883199502182914----aA123456",…}]
0
: 
{username: "440883199502182914", password: "aA123456", name: "440883199502182914----aA123456",…}
children
: 
[{name: "新质生产力与现代化产业体系", disabled: false, id: "11"},…]
0
: 
{name: "新质生产力与现代化产业体系", disabled: false, id: "11"}
1
: 
{name: "人工智能赋能制造业高质量发展", disabled: false, id: "12"}
name
: 
"440883199502182914----aA123456"
password
: 
"aA123456"
username
: 
"440883199502182914"
status
: 
"success"
然后可以选择查课结果需要学习的课程，然后提交
请求 URL
http:// **************:9900/api/order/buy
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-origin
*
cache-control
no-cache, private
connection
keep-alive
content-encoding
gzip
content-type
text/html; charset=UTF-8
date
Sat, 21 Jun 2025 16:50:17 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding
负载：
{websiteNumber: 10330101,…}
data
: 
[{username: "440883199502182914", password: "aA123456", name: "440883199502182914----aA123456",…}]
0
: 
{username: "440883199502182914", password: "aA123456", name: "440883199502182914----aA123456",…}
children
: 
[{name: "新质生产力与现代化产业体系", disabled: false, id: "11", selected: true}]
name
: 
"440883199502182914----aA123456"
password
: 
"aA123456"
selected
: 
true
username
: 
"440883199502182914"
websiteNumber
: 
10330101
，返回：
{code: 200, status: "success",…}
code
: 
200
data
: 
{message: "下单成功", orderList: [{orderId: "202506220050045801", username: "440883199502182914"}]}
message
: 
"下单成功"
orderList
: 
[{orderId: "202506220050045801", username: "440883199502182914"}]
0
: 
{orderId: "202506220050045801", username: "440883199502182914"}
orderId
: 
"202506220050045801"
username
: 
"440883199502182914"
status
: 
"success"
这就是下单成功了


订单列表：
请求 URL
http:// **************:9900/api/order/list
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-origin
*
cache-control
no-cache, private
connection
keep-alive
content-encoding
gzip
content-type
text/html; charset=UTF-8
date
Sat, 21 Jun 2025 16:51:49 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding
负载：
{pageSize: 10, pageNum: 1, status: "all", websiteNumber: "", orderNumber: "", username: "", name: ""}
name
: 
""
orderNumber
: 
""
pageNum
: 
1
pageSize
: 
10
status
: 
"all"
username
: 
""
websiteNumber
: 
""
返回：订单信息：
{code: 200, status: "success",…}
code
: 
200
data
: 
{list: [{number: "202506220050045801", websiteNumber: 10330101, websiteName: "广东省教师继续教育信息管理平台",…},…],…}
list
: 
[{number: "202506220050045801", websiteNumber: 10330101, websiteName: "广东省教师继续教育信息管理平台",…},…]
0
: 
{number: "202506220050045801", websiteNumber: 10330101, websiteName: "广东省教师继续教育信息管理平台",…}
1
: 
{number: "202506212239062301", websiteNumber: 12010101, websiteName: "甘肃省天水市专业技术人员继续教育网络平台-天水博通职业培训学校",…}
2
: 
{number: "202506212236077301", websiteNumber: 11400301, websiteName: "甘肃中经智源继续教育平台-临夏回族自治州",…}
3
: 
{number: "202506212236066501", websiteNumber: 11400301, websiteName: "甘肃中经智源继续教育平台-临夏回族自治州",…}
4
: 
{number: "202506212235304202", websiteNumber: 11400301, websiteName: "甘肃中经智源继续教育平台-临夏回族自治州",…}
5
: 
{number: "202506212235302801", websiteNumber: 11400301, websiteName: "甘肃中经智源继续教育平台-临夏回族自治州",…}
6
: 
{number: "202506210103443501", websiteNumber: 12010101, websiteName: "甘肃省天水市专业技术人员继续教育网络平台-天水博通职业培训学校",…}
7
: 
{number: "202506210102317501", websiteNumber: 12010101, websiteName: "甘肃省天水市专业技术人员继续教育网络平台-天水博通职业培训学校",…}
8
: 
{number: "202506201440425101", websiteNumber: 12010101, websiteName: "甘肃省天水市专业技术人员继续教育网络平台-天水博通职业培训学校",…}
9
: 
{number: "202506201438195701", websiteNumber: 11400301, websiteName: "甘肃中经智源继续教育平台-临夏回族自治州",…}
total
: 
306
status
: 
"success"
有很多页，示例只展现第一页的信息


通过账号搜索订单详细信息：请求 URL
http:// **************:9900/api/order/list
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-origin
*
cache-control
no-cache, private
connection
keep-alive
content-encoding
gzip
content-type
text/html; charset=UTF-8
date
Fri, 20 Jun 2025 08:40:09 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding
accept
application/json
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer 62GkwcYWHjzCx0m07FcWJvV5WHohrkDjuBJsHeMr
content-length
120
content-type
application/json
host
 **************:9900
origin
http://*************:9988
proxy-connection
keep-alive
referer
http://*************:9988/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
负载：{pageSize: 10, pageNum: 1, status: "all", websiteNumber: "", orderNumber: "",…}
name
: 
""
orderNumber
: 
""
pageNum
: 
1
pageSize
: 
10
status
: 
"all"
username
: 
"620503198212047424"
websiteNumber
: 
""
其中username是我搜索的账号
返回学习数据：{code: 200, status: "success", data: {list: [,…], total: 1}}
code
: 
200
data
: 
{list: [,…], total: 1}
list
: 
[,…]
0
: 
{number: "202506191836204601", websiteNumber: 12010101, websiteName: "甘肃省天水市专业技术人员继续教育网络平台-天水博通职业培训学校",…}
aftersaleStatus
: 
0
courses
: 
[{name: "2025", disabled: false, id: "11", selected: true}]
0
: 
{name: "2025", disabled: false, id: "11", selected: true}
disabled
: 
false
id
: 
"11"
name
: 
"2025"
selected
: 
true
createDate
: 
"2025-06-19 18:36:20"
name
: 
"霍永妹"
number
: 
"202506191836204601"
password
: 
"047424*"
payment
: 
"0.80"
progress
: 
"学习完成"
status
: 
1
updateDate
: 
"2025-06-19 18:38:27"
url
: 
"https://www.tsbtgs.cn/"
username
: 
"620503198212047424"
websiteName
: 
"甘肃省天水市专业技术人员继续教育网络平台-天水博通职业培训学校"
websiteNumber
: 
12010101
total
: 
1
status
: 
"success"

下单：


本站所有可学习的项目的api：

请求 URL
http:// **************:9900/api/website/list
请求方法
POST
状态代码
200 OK
远程地址
127.0.0.1:33210
引用站点策略
strict-origin-when-cross-origin
access-control-allow-origin
*
cache-control
no-cache, private
connection
keep-alive
content-encoding
gzip
content-type
text/html; charset=UTF-8
date
Sun, 10 Aug 2025 13:26:05 GMT
keep-alive
timeout=4
proxy-connection
keep-alive
server
nginx
transfer-encoding
chunked
vary
Accept-Encoding
accept
application/json
accept-encoding
gzip, deflate
accept-language
zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
authorization
Bearer CQZ9xzRjmo3rznfUzjssgAmbfaSflYuX9ykgcQAD
content-length
37
content-type
application/json
host
 **************:9900
origin
http://*************:9988
proxy-connection
keep-alive
referer
http://*************:9988/
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********


负载：
{websiteName: "", websiteNumber: ""}
websiteName
: 
""
websiteNumber
: 
""

返回：
{code: 200, status: "success",…}
code
: 
200
data
: 
[{id: 1, number: 10010101, search: "国培网-融学Web-https://web.chinahrt.com", name: "国培网-融学Web",…},…]
[0 … 99]
0
: 
{id: 1, number: 10010101, search: "国培网-融学Web-https://web.chinahrt.com", name: "国培网-融学Web",…}
date
: 
"2023-07-26 10:00:00"
description
: 
"大概1至2小时完成"
format
: 
"账号 密码"
id
: 
1
isExam
: 
1
isSearchCourse
: 
1
must
: 
1
name
: 
"国培网-融学Web"
number
: 
10010101
price
: 
0.2
rate
: 
1
remark
: 
"无"
search
: 
"国培网-融学Web-https://web.chinahrt.com"
status
: 
1
unit
: 
"年度"
unitId
: 
1
url
: 
"https://web.chinahrt.com"
1
: 
{id: 191, number: 10010102, search: "国培网-融学App【秒学版】-https://gp.chinahrt.com/", name: "国培网-融学App【秒学版】",…}
date
: 
"2024-07-05 00:15:11"
description
: 
"每培训10分钟内完成"
format
: 
"账号 密码"
id
: 
191
isExam
: 
1
isSearchCourse
: 
1
must
: 
1
name
: 
"国培网-融学App【秒学版】"
number
: 
10010102
price
: 
0.2
rate
: 
2
remark
: 
""
search
: 
"国培网-融学App【秒学版】-https://gp.chinahrt.com/"
status
: 
1
unit
: 
"培训"
unitId
: 
2
url
: 
"https://gp.chinahrt.com/"
2
: 
{id: 2, number: 10010201, search: "国培网-融学-GP-http://gp.chinahrt.com/", name: "国培网-融学-GP",…}
3
: 
{id: 119, number: 10010202, search: "国培网-融学Gp-西藏日喀则市专业技术人员继续教育网-http://rikazezj.chinahrt.com/",…}
4
: 
{id: 218, number: 10010203, search: "国培网-融学-GP-常速版-http://gp.chinahrt.com/", name: "国培网-融学-GP-常速版",…}
5
: 
{id: 76, number: 10010301, search: "国培网-融学-河北省专业技术人员继续教育管理系统-https://hbzyjs.chinahrt.com/",…}
6
: 
{id: 220, number: 10010501, search: "国培网-吉林创联专业技术人员继续教育网-https://jilinzj.chinahrt.com/",…}
7
: 
{id: 251, number: 10010601, search: "国培网-重庆市专技人员继续教育公需科目网络学习服务平台-http://cq.rsbsyzx.cn/",…}
8
: 
{id: 410, number: 10010701, search: "国培网-河南省会计人员继续教育网-https://hnkj.chinahrt.com/",…}
9
: 
{id: 3, number: 10020101, search: "杭州专业技术人员新干线-https://learning.hzrs.hangzhou.gov.cn/",…}
10
: 
{id: 5, number: 10030101, search: "甘肃省公务员网络培训-https://gwypx.gsdj.gov.cn/", name: "甘肃省公务员网络培训",…}
11
: 
{id: 6, number: 10040101, search: "浙江省住房和城乡建设行业专业技术人员继续教育系统-https://zj.zjjsrc.cn/",…}
12
: 
{id: 58, number: 10040201, search: "浙江省二级建造师继续教育-https://2j.zjjsrc.cn/", name: "浙江省二级建造师继续教育",…}
13
: 
{id: 109, number: 10040301, search: "浙江省建筑施工企业三类人员继续教育-https://abc.zjjsrc.cn/",…}
14
: 
{id: 7, number: 10050101, search: "安徽专业技术人员继续教育在线-https://www.zjzx.ah.cn/", name: "安徽专业技术人员继续教育在线",…}
15
: 
{id: 8, number: 10060101, search: "丽水人力社保专业技术人员继续教育培训-https://rlzy.lshrss.cn",…}
16
: 
{id: 9, number: 10070201, search: "中安在线安全培训学院-北京经济技术开发区城市运行局安全生产培训服务平台-https://jjkfq.100anquan.com/",…}
17
: 
{id: 23, number: 10070301, search: "中安在线安全培训学院-四川省交通工程技术人员继续教育平台-https://scjg.100anquan.com/",…}
18
: 
{id: 82, number: 10070401, search: "中安在线安全培训学院-北京市职业卫生培训管理平台-https://bjzyjk.100anquan.com/",…}
19
: 
{id: 112, number: 10070501, search: "中安在线安全培训学院-新安创科安全生产在线培训平台-https://xack.100anquan.com/",…}
20
: 
{id: 355, number: 10070601, search: "中安在线安全培训学院-丹东市安途职业技能培训学校-https://antu.100anquan.com/",…}
21
: 
{id: 363, number: 10070701, search: "中安在线安全培训学院-助安在线-https://zhdz.100anquan.com/",…}
22
: 
{id: 438, number: 10070801, search: "中安在线安全培训学院-东丰县东沣通达职业技能培训有限公司网络培训平台-https://sydf.100anquan.com",…}
23
: 
{id: 10, number: 10080101, search: "绍兴市专业技术人员继续教育平台-http://220.191.224.159", name: "绍兴市专业技术人员继续教育平台",…}
24
: 
{id: 11, number: 10090101, search: "环安人才-安徽省专业技术人员继续教育在线学习平台-https://jxjy.ahharc.com/",…}
25
: 
{id: 12, number: 10100101, search: "成都市专业技术人员继续教育基地（市政）-https://www.cdzjjj.cn/",…}
26
: 
{id: 13, number: 10110101, search: "希沃学苑-https://study.seewoedu.cn/", name: "希沃学苑", format: "账号 密码",…}
27
: 
{id: 14, number: 10120101, search: "山东开放大学-山东省省直专业技术人员继续教育公需课平台-课程包-https://szzj.sdou.edu.cn/cms/",…}
28
: 
{id: 46, number: 10120102, search: "山东开放大学-山东省省直专业技术人员继续教育公需课平台-课程-https://szzj.sdou.edu.cn/cms/",…}
29
: 
{id: 15, number: 10130101, search: "中国继续医学教育网-https://www.ncme.org.cn", name: "中国继续医学教育网",…}
30
: 
{id: 18, number: 10140101, search: "海峡培训平台-福建省专业技术人才人员继续教育平台-课程包-https://fj.rcpxpt.com/",…}
31
: 
{id: 22, number: 10140102, search: "海峡培训平台-福建省专业技术人才人员继续教育平台-课程-https://fj.rcpxpt.com/",…}
32
: 
{id: 48, number: 10140103, search: "海峡培训平台-福建省专业技术人才人员继续教育平台-课程包（选修课）-https://fj.rcpxpt.com/",…}
33
: 
{id: 16, number: 10150101, search: "乌鲁木齐建设职业培训中心-https://www.wlmqcol.com/", name: "乌鲁木齐建设职业培训中心",…}
34
: 
{id: 17, number: 10160101, search: "安徽药品人才网-https://www.ahfda.com/", name: "安徽药品人才网", format: "账号 密码",…}
35
: 
{id: 19, number: 10170101, search: "浙江省专业技术人员继续教育学时登记管理系统-https://zjjx.rlsbt.zj.gov.cn/",…}
36
: 
{id: 232, number: 10170102, search: "浙江省专业技术人员继续教育学时登记管理系统-专业课-https://zjjx.rlsbt.zj.gov.cn/",…}
37
: 
{id: 21, number: 10180101, search: "德阳市继续教育公需科目培训平台-https://px.dyhrsc.cn/", name: "德阳市继续教育公需科目培训平台",…}
38
: 
{id: 25, number: 10190101, search: "咨询工程师（投资）继续教育平台-https://zxgcsjxjy.lanmaiedu.com/",…}
39
: 
{id: 24, number: 10210101, search: "内蒙古继续教育-https://nmgjxjy.com/", name: "内蒙古继续教育", format: "账号 密码",…}
40
: 
{id: 26, number: 10220101, search: "专技天下-https://www.zgzjzj.com", name: "专技天下", format: "账号 密码",…}
41
: 
{id: 69, number: 10220102, search: "专技天下【自购课】-https://www.zgzjzj.com", name: "专技天下【自购课】",…}
42
: 
{id: 27, number: 10240101,…}
43
: 
{id: 35, number: 10240201,…}
44
: 
{id: 63, number: 10240301, search: "交通运输工程监理工程师管理服务系统-https://jtgcjl.jtzyzg.org.cn/",…}
45
: 
{id: 90, number: 10240401,…}
46
: 
{id: 28, number: 10250101, search: "湖南开放大学专业技术人员继续教育网-湖南省-http://www.hnzjpx.net/",…}
47
: 
{id: 186, number: 10250102, search: "湖南开放大学专业技术人员继续教育网-湖南省-课程-http://www.hnzjpx.net/",…}
48
: 
{id: 29, number: 10250201, search: "湖南开放大学专业技术人员继续教育网-怀化市-http://huaihua.hnzjpx.net/",…}
49
: 
{id: 30, number: 10270101, search: "正保会计网校-通用版【姓名-身份证】-https://jxjy.chinaacc.com/",…}
50
: 
{id: 61, number: 10270201, search: "正保会计网校-江西省-https://jxjy.chinaacc.com/jiangxisheng",…}
51
: 
{id: 187, number: 10270202, search: "正保会计网校-江西省-全学-https://jxjy.chinaacc.com/jiangxisheng",…}
52
: 
{id: 80, number: 10270301, search: "正保会计网校-浙江-宁波-https://jxjy.chinaacc.com/zhejiang/ningbo",…}
53
: 
{id: 86, number: 10270401, search: "正保会计网校-浙江省-https://jxjy.chinaacc.com/zhejiang/sheng2019/",…}
54
: 
{id: 87, number: 10270501, search: "正保会计网校-广西壮族自治区-https://jxjy.chinaacc.com/guangxi/guangxiqu/",…}
55
: 
{id: 101, number: 10270601, search: "正保会计网校-甘肃省-https://jxjy.chinaacc.com/gansu/sheng/",…}
56
: 
{id: 279, number: 10270701, search: "正保会计网校【通用版】【9:00-21:00】-https://jxjy.chinaacc.com/",…}
57
: 
{id: 34, number: 10280101, search: "安徽专技网-https://ah.peixun.city/", name: "安徽专技网", format: "账号 密码",…}
58
: 
{id: 31, number: 10290101, search: "重庆人社培训网-公需课-https://www.cqrspx.cn/", name: "重庆人社培训网-公需课",…}
59
: 
{id: 204, number: 10290102, search: "重庆人社培训网-公需课-渝快办登录-https://www.cqrspx.cn/",…}
60
: 
{id: 32, number: 10300101, search: "贵州省专业技术人员继续教育平台-公需课-http://www.gzjxjy.gzsrs.cn/",…}
61
: 
{id: 126, number: 10300102, search: "贵州省专业技术人员继续教育平台-初任课程-https://gzjxjy.gzsrs.cn/",…}
62
: 
{id: 33, number: 10310101, search: "金华人社专业技术人员继续教育-https://jxjy.rsj.jinhua.gov.cn/",…}
63
: 
{id: 60, number: 10310201, search: "金华公务员网络学院-https://wlpx.rsj.jinhua.gov.cn/", name: "金华公务员网络学院",…}
64
: 
{id: 36, number: 10320101, search: "广西普法云平台-https://exam.gxpf.cn/", name: "广西普法云平台", format: "账号 密码",…}
65
: 
{id: 274, number: 10320102, search: "广西普法云平台【高分版】-https://exam.gxpf.cn/", name: "广西普法云平台【高分版】",…}
66
: 
{id: 37, number: 10330101, search: "广东省教师继续教育信息管理平台-https://jsglpt.gdedu.gov.cn/",…}
67
: 
{id: 38, number: 10340101, search: "平顶山市专业技术人员在线学习平台-http://pds.hnjxedu.org.cn/",…}
68
: 
{id: 103, number: 10350101, search: "东奥会计-江西省-https://jiangxi.dongao.cn/", name: "东奥会计-江西省",…}
69
: 
{id: 118, number: 10350102, search: "东奥会计-湖北省-https://hubeisheng.dongao.cn/", name: "东奥会计-湖北省",…}
70
: 
{id: 163, number: 10350103, search: "东奥会计-通用版-https://www.dongao.cn/jxjy", name: "东奥会计-通用版",…}
71
: 
{id: 173, number: 10350104, search: "东奥会计-广西-https://guangxi.dongao.cn/", name: "东奥会计-广西",…}
72
: 
{id: 282, number: 10350105, search: "东奥会计-浙江省-https://zhejiang.dongao.cn/", name: "东奥会计-浙江省",…}
73
: 
{id: 293, number: 10350106, search: "东奥会计-浙江省-宁波市-https://ningbo.dongao.cn/", name: "东奥会计-浙江省-宁波市",…}
74
: 
{id: 393, number: 10350107, search: "东奥会计-App-https://www.dongao.cn/jxjy", name: "东奥会计-App",…}
75
: 
{id: 39, number: 10360101, search: "灯塔-山东干部网络学院-专题班-https://gbwlxy.dtdjzx.gov.cn/",…}
76
: 
{id: 40, number: 10360102, search: "灯塔-山东干部网络学院【自定义学时】-https://gbwlxy.dtdjzx.gov.cn/",…}
77
: 
{id: 41, number: 10360103, search: "灯塔-山东干部网络学院-专栏-https://gbwlxy.dtdjzx.gov.cn/",…}
78
: 
{id: 299, number: 10360201, search: "灯塔-山东党员网络学院【自定义学时】-https://dywlxy.dtdjzx.gov.cn/",…}
79
: 
{id: 42, number: 10370101, search: "新疆维吾尔自治区执业药师协会【北京金航联科技发展有限公司】-http://mtnet.xjzyysxh.cn/",…}
80
: 
{id: 57, number: 10370201,…}
81
: 
{id: 62, number: 10370301, search: "新疆维吾尔自治区执业药师协会【公需】【新疆继续教育公需科目平台】-http://mtnet.xjzyysxh.cn/login",…}
82
: 
{id: 425, number: 10370401, search: "新疆维吾尔自治区执业药师协会【新疆医科大学国家级医学继续教育能力提升平台】-http://mtnet.xjzyysxh.cn/",…}
83
: 
{id: 43, number: 10380101, search: "重庆信息通信人才培训网-http://www.cqtxpx.com/#/login", name: "重庆信息通信人才培训网",…}
84
: 
{id: 44, number: 10390101, search: "四川省执业药师协会-成都和航科技有限公司-http://sclpa.mtnet.com.cn/",…}
85
: 
{id: 141, number: 10390201, search: "四川省执业药师协会-北京金航联科技发展有限公司-http://sclpa.mtnet.com.cn/",…}
86
: 
{id: 45, number: 10400201, search: "59iedu甘肃专技-临夏州-https://gslxzj.59iedu.com/index",…}
87
: 
{id: 95, number: 10400301, search: "59iedu福建专技-http://xmrc.59iedu.com/index", name: "59iedu福建专技",…}
88
: 
{id: 98, number: 10400401, search: "59iedu福建药师协会培训-https://fjysxhpx.59iedu.com/index",…}
89
: 
{id: 104, number: 10400501, search: "59iedu福建省专业技术人员继续教育网络平台-https://zjpx.59iedu.com/index",…}
90
: 
{id: 165, number: 10400601, search: "59iedu安徽省专业技术人员继续教育公需课培训平台-https://ahzj.59iedu.com/",…}
91
: 
{id: 182, number: 10400701, search: "59iedu福建省建设从业人员网络教育培训平台-https://fjjsrckjjy.59iedu.com/index",…}
92
: 
{id: 194, number: 10400801, search: "淮安市专业技术人员继续教育基地-https://jshazj.59iedu.com/",…}
93
: 
{id: 200, number: 10400901, search: "59iedu甘肃专技-甘南州-https://gsgnzj.59iedu.com/",…}
94
: 
{id: 443, number: 10401001, search: "59iedu河南省会计专业技术人员继续教育-https://hnkj.59iedu.com/",…}
95
: 
{id: 449, number: 10401101, search: "泉州提高教育中心网络学习平台-https://www.qzjxjy.com/", name: "泉州提高教育中心网络学习平台",…}
96
: 
{id: 456, number: 10401201, search: "南平市网络培训远程教育平台-http://nanping.59iedu.com", name: "南平市网络培训远程教育平台",…}
97
: 
{id: 47, number: 10410101, search: "山财培训网-山东财经大学继续教育学院【通用版】-http://training.sdufe.edu.cn/",…}
98
: 
{id: 49, number: 10410201, search: "山财培训网-聊城市会计人员网络继续教育管理平台-http://liaocheng.training.sdufe.edu.cn/",…}
99
: 
{id: 50, number: 10420101, search: "贵州省党员干部网络学院-专题班【正常版】-https://gzwy.gov.cn/",…}
[100 … 199]
[200 … 299]
[300 … 399]
[400 … 463]
status
: 
"success"   具体细节省略


其中
date
: 
"2023-07-26 10:00:00"
description（课程寿命）
: 
"大概1至2小时完成"
format
: 
"账号 密码"
id
: 
1
isExam
: 
1
isSearchCourse
: 
1
must
: 
1
name
: 
"国培网-融学Web"
number
: 
10010101
price
: 
0.2
rate
: 
1
remark
: 
"无"
search
: 
"国培网-融学Web-https://web.chinahrt.com"
status
: 
1
unit
: 
"年度"
unitId
: 
1
url
: 
"https://web.chinahrt.com"


上面对应的就是易教育的课程说明是这种格式：
下单详情
名称：国培网-融学Web
网址：https://web.chinahrt.com
格式：账号 密码
价格：0.2 / 年度
速率：加速
考试：支持
查课：支持
说明：大概1至2小时完成
状态：正常

number
: 
10010101   是站点编号