<?php
/**
 * 新订单快速入队脚本 - 确保新订单30-40秒内提交到上游
 * 每1分钟执行一次，专注于新订单的快速处理
 */

// 引入公共配置文件
include(dirname(__FILE__) . '/../confing/common.php');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

$log_file = dirname(__FILE__) . '/auto_enqueue.log';
$max_log_size = 10 * 1024 * 1024; // 10MB

// 日志函数
function writeLog($message) {
    global $log_file, $max_log_size;
    
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] {$message}\n";
    
    // 检查日志文件大小，如果超过限制则清空
    if (file_exists($log_file) && filesize($log_file) > $max_log_size) {
        file_put_contents($log_file, '');
    }
    
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
    echo $log_message;
}

writeLog("=== 新订单快速入队脚本开始执行 ===");

try {
    // 连接Redis
    $redis = new Redis();
    $redis->connect("127.0.0.1", "6379");
    $redis->select(7); // 选择数据库7
    
    $rediscode = $redis->ping();
    if ($rediscode != true) {
        writeLog("❌ Redis连接失败");
        exit(1);
    }
    
    writeLog("✅ Redis连接成功");
    
    // 检查当前队列长度
    $current_queue_length = $redis->llen("oidsydcl");
    writeLog("当前队列长度: {$current_queue_length}");
    
    // 如果队列中已经有很多订单，就不再添加新的
    if ($current_queue_length > 50) {
        writeLog("⚠️ 队列中订单过多({$current_queue_length})，跳过本次入队");
        exit(0);
    }
    
    // 查询需要同步进度的订单
    // 优先处理新订单，确保30-40秒内提交到上游
    // 条件：
    // 1. 对接状态为1（已对接）
    // 2. 状态为进行中的订单（不包括已完成、已退款等终态）
    // 3. 优先处理新订单（最近5分钟的订单）
    $sql = "
        SELECT oid, user, kcname, yid, status, process, addtime, hid,
               TIMESTAMPDIFF(MINUTE, addtime, NOW()) as minutes_old
        FROM qingka_wangke_order
        WHERE dockstatus = 1
        AND status IN ('待处理', '上号中', '待上号', '已提取', '已提交', '登录中', '考试中', '补刷中', '运行中', '进行中', '学习中', '队列中')
        AND (
            -- 新订单（5分钟内）：立即处理
            (addtime > DATE_SUB(NOW(), INTERVAL 5 MINUTE))
            OR
            -- 老订单：需要进度更新的
            (
                addtime <= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
                AND (
                    process = '获取失败'
                    OR process = 'chusheng-获取失败'
                    OR process IS NULL
                    OR process = ''
                    OR process = '0%'
                    OR (process NOT LIKE '%100%' AND addtime > DATE_SUB(NOW(), INTERVAL 2 HOUR))
                )
            )
        )
        ORDER BY
            CASE WHEN addtime > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 0 ELSE 1 END,  -- 新订单优先
            addtime DESC
        LIMIT 30
    ";
    
    $result = $DB->query($sql);
    
    if (!$result) {
        writeLog("❌ 数据库查询失败");
        exit(1);
    }
    
    $orders_to_enqueue = [];
    $total_orders = 0;
    
    while ($row = $result->fetch_assoc()) {
        $total_orders++;
        
        // 检查订单是否已经在队列中
        $oid = $row['oid'];
        $queue_items = $redis->lrange("oidsydcl", 0, -1);
        
        if (!in_array($oid, $queue_items)) {
            $orders_to_enqueue[] = $oid;
            writeLog("准备入队: 订单{$oid} - {$row['user']} - {$row['status']} - {$row['process']}");
        } else {
            writeLog("跳过: 订单{$oid}已在队列中");
        }
    }
    
    writeLog("扫描到{$total_orders}个订单，其中" . count($orders_to_enqueue) . "个需要快速入队");
    
    // 将订单加入队列
    $enqueued_count = 0;
    foreach ($orders_to_enqueue as $oid) {
        $redis->lPush("oidsydcl", $oid);
        $enqueued_count++;
    }
    
    if ($enqueued_count > 0) {
        writeLog("✅ 成功快速入队{$enqueued_count}个新订单");
        
        // 更新订单状态为"待更新"，避免重复入队
        if (!empty($orders_to_enqueue)) {
            $oid_list = implode(',', $orders_to_enqueue);
            $update_sql = "UPDATE qingka_wangke_order SET status = '待更新' WHERE oid IN ({$oid_list}) AND status != '待更新'";
            $DB->query($update_sql);
            writeLog("更新{$enqueued_count}个订单状态为'待更新'");
        }
    } else {
        writeLog("ℹ️ 没有新订单需要入队");
    }
    
    // 检查守护进程状态
    $daemon_processes = [
        'chusheng1.php',
        'chusheng2.php', 
        'chusheng3.php'
    ];
    
    $running_daemons = 0;
    foreach ($daemon_processes as $process) {
        $check_cmd = "ps aux | grep '{$process}' | grep -v grep";
        $output = shell_exec($check_cmd);
        if (!empty($output)) {
            $running_daemons++;
        }
    }
    
    writeLog("守护进程状态: {$running_daemons}/{count($daemon_processes)} 个进程在运行");
    
    if ($running_daemons == 0) {
        writeLog("⚠️ 警告: 没有守护进程在运行！");
    }
    
    // 显示最终队列状态
    $final_queue_length = $redis->llen("oidsydcl");
    writeLog("最终队列长度: {$final_queue_length}");
    
    writeLog("=== 新订单快速入队脚本执行完成 ===");
    
} catch (Exception $e) {
    writeLog("❌ 异常: " . $e->getMessage());
    writeLog("文件: " . $e->getFile());
    writeLog("行号: " . $e->getLine());
    exit(1);
}

?>
