-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.8.5
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2024-06-07 18:44:19
-- 服务器版本： 5.7.26
-- PHP 版本： 7.3.4

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `baishancha`
--

-- --------------------------------------------------------

--
-- 表的结构 `qingka_wangke_jxjyclass`
--

CREATE TABLE `qingka_wangke_jxjyclass` (
  `id` int(11) NOT NULL,
  `number` varchar(255) NOT NULL COMMENT '对接站ID',
  `name` varchar(255) NOT NULL,
  `format` varchar(255) NOT NULL COMMENT '下单格式',
  `url` varchar(255) NOT NULL COMMENT 'url',
  `price` varchar(255) NOT NULL COMMENT '价格',
  `unitId` varchar(255) NOT NULL,
  `must` varchar(255) NOT NULL,
  `unit` varchar(255) NOT NULL COMMENT '价格类型',
  `rate` varchar(255) NOT NULL COMMENT '速率',
  `isExam` varchar(255) NOT NULL COMMENT '考试',
  `isSearchCourse` varchar(255) NOT NULL COMMENT '查课',
  `status` int(11) NOT NULL COMMENT '状态',
  `description` varchar(255) NOT NULL COMMENT '描述',
  `remark` varchar(255) NOT NULL,
  `date` varchar(255) NOT NULL COMMENT '日期'
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

--
-- 转储表的索引
--

--
-- 表的索引 `qingka_wangke_jxjyclass`
--
ALTER TABLE `qingka_wangke_jxjyclass`
  ADD PRIMARY KEY (`id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `qingka_wangke_jxjyclass`
--
ALTER TABLE `qingka_wangke_jxjyclass`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
