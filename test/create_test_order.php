<?php
/**
 * 创建测试订单验证修复效果
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>创建测试订单验证修复效果</h1>\n";
echo "<pre>\n";

// 获取易教育项目
echo "=== 获取易教育项目 ===\n";
$jxjy_project = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE docking=50 AND status=1 LIMIT 1");

if (!$jxjy_project) {
    echo "❌ 没有找到易教育项目\n";
    exit;
}

echo "使用项目:\n";
echo "  cid: {$jxjy_project['cid']}\n";
echo "  name: {$jxjy_project['name']}\n";
echo "  docking: {$jxjy_project['docking']}\n";
echo "  noun: '{$jxjy_project['noun']}'\n";

// 创建测试订单
echo "\n=== 创建测试订单 ===\n";
$current_time = date('Y-m-d H:i:s');
$test_user = 'test_user_' . time();
$test_course = 'test_course_' . time();

$insert_sql = "INSERT INTO qingka_wangke_order (uid, cid, hid, ptname, school, name, user, pass, kcid, kcname, courseEndTime, fees, noun, miaoshua, addtime, ip, dockstatus, status, process) VALUES ('1', '{$jxjy_project['cid']}', '50', '{$jxjy_project['name']}', '自动识别', '测试用户', '{$test_user}', 'test_pass', '{$test_course}', '测试课程', '2025-12-31', '1.00', '{$jxjy_project['noun']}', '0', '{$current_time}', '127.0.0.1', '0', '已提交', '0%')";

echo "插入SQL:\n";
echo substr($insert_sql, 0, 200) . "...\n";

if ($DB->query($insert_sql)) {
    $test_oid = $DB->lastInsertId();
    echo "\n✅ 测试订单创建成功\n";
    echo "订单ID: {$test_oid}\n";
    
    // 验证订单数据
    $created_order = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE oid='{$test_oid}' LIMIT 1");
    echo "\n订单详情:\n";
    echo "  oid: {$created_order['oid']}\n";
    echo "  cid: {$created_order['cid']}\n";
    echo "  hid: {$created_order['hid']}\n";
    echo "  noun: '{$created_order['noun']}'\n";
    echo "  dockstatus: {$created_order['dockstatus']}\n";
    echo "  status: {$created_order['status']}\n";
    
    // 等待处理
    echo "\n=== 等待处理 ===\n";
    echo "等待addchu.php处理订单...\n";
    
    $max_wait = 30; // 最多等待30秒
    $waited = 0;
    $processed = false;
    
    while ($waited < $max_wait) {
        sleep(2);
        $waited += 2;
        
        // 检查订单状态
        $current_order = $DB->get_row("SELECT dockstatus, status, yid FROM qingka_wangke_order WHERE oid='{$test_oid}' LIMIT 1");
        
        echo "等待 {$waited}s - dockstatus: {$current_order['dockstatus']}, status: {$current_order['status']}\n";
        
        if ($current_order['dockstatus'] == '1') {
            echo "✅ 订单处理成功！\n";
            echo "  最终状态: {$current_order['status']}\n";
            echo "  上游订单ID: {$current_order['yid']}\n";
            $processed = true;
            break;
        } elseif ($current_order['dockstatus'] == '2') {
            echo "❌ 订单处理失败\n";
            break;
        }
    }
    
    if (!$processed && $waited >= $max_wait) {
        echo "⏰ 等待超时，订单可能还在队列中\n";
    }
    
    // 检查日志
    echo "\n=== 检查处理日志 ===\n";
    $log_file = dirname(__FILE__) . '/../redis/addchu.log';
    if (file_exists($log_file)) {
        $log_content = file_get_contents($log_file);
        $recent_logs = array_slice(explode("\n", $log_content), -10); // 最后10行
        
        echo "最近的处理日志:\n";
        foreach ($recent_logs as $line) {
            if (trim($line)) {
                echo "  " . trim($line) . "\n";
            }
        }
        
        // 检查是否还有"项目信息不存在"错误
        if (strpos($log_content, '项目信息不存在') !== false) {
            echo "\n⚠️  日志中仍有'项目信息不存在'错误\n";
        } else {
            echo "\n✅ 日志中没有'项目信息不存在'错误\n";
        }
    } else {
        echo "日志文件不存在: {$log_file}\n";
    }
    
    // 清理测试订单
    echo "\n=== 清理测试订单 ===\n";
    if (isset($_GET['keep']) && $_GET['keep'] == '1') {
        echo "保留测试订单用于进一步调试\n";
    } else {
        $DB->query("DELETE FROM qingka_wangke_order WHERE oid = '{$test_oid}'");
        echo "✅ 测试订单已删除\n";
    }
    
} else {
    echo "❌ 测试订单创建失败: " . $DB->error() . "\n";
}

echo "\n=== 测试总结 ===\n";
if (isset($processed) && $processed) {
    echo "🎉 修复成功！订单能正常提交到上游\n";
    echo "\n修复效果:\n";
    echo "- ✅ 不再出现'项目信息不存在'错误\n";
    echo "- ✅ 订单能正常提交到易教育上游\n";
    echo "- ✅ 订单状态正常更新\n";
} else {
    echo "❌ 修复可能还有问题\n";
    echo "\n建议:\n";
    echo "1. 检查addchu.php的详细日志\n";
    echo "2. 确认进程是否正常运行\n";
    echo "3. 验证网络连接和API状态\n";
}

echo "\n如果要保留测试订单，请访问: " . $_SERVER['REQUEST_URI'] . "?keep=1\n";

echo "\n</pre>\n";
?>
