<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始调试易教育进度同步...\n";

try {
    echo "1. 包含配置文件...\n";
    include('confing/common.php');
    echo "✅ 配置文件加载成功\n";
    
    echo "2. 测试数据库连接...\n";
    if ($DB) {
        echo "✅ 数据库连接成功\n";
    } else {
        echo "❌ 数据库连接失败\n";
        exit;
    }
    
    echo "3. 查询易教育订单...\n";
    $sql = "SELECT o.oid, o.user, o.status, o.process, h.pt 
            FROM qingka_wangke_order o 
            LEFT JOIN qingka_wangke_huoyuan h ON o.hid = h.hid 
            WHERE h.pt = 'jxjy' 
            ORDER BY o.addtime DESC 
            LIMIT 1";
    
    $result = $DB->query($sql);
    if ($result) {
        $order = $DB->fetch($result);
        if ($order) {
            echo "✅ 找到易教育订单: {$order['oid']}\n";
            echo "   用户: {$order['user']}\n";
            echo "   状态: {$order['status']}\n";
            echo "   进度: {$order['process']}\n";
            
            echo "4. 包含必要的函数文件...\n";
            include('Checkorder/configuration.php');
            echo "✅ 配置函数加载成功\n";
            include('Checkorder/jdjk.php');
            echo "✅ 进度查询函数加载成功\n";
            
            echo "5. 测试进度查询...\n";
            $progress_result = processCx($order['oid']);
            
            echo "6. 查询结果:\n";
            if (is_array($progress_result)) {
                foreach ($progress_result as $i => $item) {
                    echo "   结果 $i:\n";
                    foreach ($item as $key => $value) {
                        echo "     $key: $value\n";
                    }
                }
            } else {
                echo "   非数组结果: ";
                var_dump($progress_result);
            }
            
        } else {
            echo "❌ 没有找到易教育订单\n";
        }
    } else {
        echo "❌ 数据库查询失败\n";
    }
    
} catch (Exception $e) {
    echo "❌ 异常: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "调试完成\n";
?>
