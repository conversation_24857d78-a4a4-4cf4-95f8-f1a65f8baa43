<?php
/**
 * 调试addWk函数的项目信息查询问题
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>调试addWk函数的项目信息查询问题</h1>\n";
echo "<pre>\n";

// 获取失败的订单ID
$failed_oid = '2147483678'; // 从错误日志中获取的订单ID

echo "=== 分析失败订单: {$failed_oid} ===\n";

// 获取订单信息
$order = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE oid='{$failed_oid}' LIMIT 1");
if (!$order) {
    echo "❌ 订单不存在: {$failed_oid}\n";
    
    // 查找最近的易教育订单
    echo "\n查找最近的易教育订单...\n";
    $recent_orders = $DB->query("SELECT oid,cid,hid,noun,user,kcname,dockstatus FROM qingka_wangke_order WHERE hid=50 ORDER BY oid DESC LIMIT 5");
    while ($row = $DB->fetch($recent_orders)) {
        echo "订单 {$row['oid']}: cid={$row['cid']}, noun='{$row['noun']}', dockstatus={$row['dockstatus']}\n";
        if ($row['dockstatus'] == 0) {
            $failed_oid = $row['oid'];
            $order = $row;
            echo "  → 使用此订单进行测试\n";
            break;
        }
    }
    
    if (!$order) {
        echo "❌ 没有找到可测试的订单\n";
        exit;
    }
}

echo "\n订单信息:\n";
echo "  oid: {$order['oid']}\n";
echo "  cid: {$order['cid']}\n";
echo "  hid: {$order['hid']}\n";
echo "  noun: '{$order['noun']}'\n";
echo "  user: {$order['user']}\n";
echo "  kcname: {$order['kcname']}\n";
echo "  dockstatus: {$order['dockstatus']}\n";

// 模拟addWk函数的逻辑
echo "\n=== 模拟addWk函数逻辑 ===\n";

$cid = $order["cid"];
$noun = $order["noun"];

echo "从订单获取的参数:\n";
echo "  cid: {$cid}\n";
echo "  noun: '{$noun}'\n";

// 获取商品信息
$class_info = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='{$cid}' LIMIT 1");
if (!$class_info) {
    echo "❌ 商品信息不存在，cid: {$cid}\n";
    exit;
} else {
    echo "\n商品信息:\n";
    echo "  cid: {$class_info['cid']}\n";
    echo "  name: {$class_info['name']}\n";
    echo "  docking: {$class_info['docking']}\n";
    echo "  noun: '{$class_info['noun']}'\n";
}

$hid = $class_info["docking"];

// 获取货源信息
$huoyuan = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$hid}' LIMIT 1");
if (!$huoyuan) {
    echo "❌ 货源信息不存在，hid: {$hid}\n";
    exit;
} else {
    echo "\n货源信息:\n";
    echo "  hid: {$huoyuan['hid']}\n";
    echo "  name: {$huoyuan['name']}\n";
    echo "  pt: {$huoyuan['pt']}\n";
}

// 问题分析：检查项目查询逻辑
echo "\n=== 问题分析 ===\n";
echo "当前的查询逻辑（有问题）:\n";
echo "  SELECT * FROM qingka_wangke_class WHERE noun = '{$noun}' AND docking = '{$hid}'\n";

$current_query_result = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE noun = '{$noun}' AND docking = '{$hid}' LIMIT 1");
if ($current_query_result) {
    echo "  ✅ 当前查询能找到项目\n";
} else {
    echo "  ❌ 当前查询找不到项目\n";
    
    // 分析原因
    echo "\n原因分析:\n";
    
    // 检查noun字段匹配
    if (empty($noun)) {
        echo "  1. 订单的noun字段为空\n";
    } else {
        $noun_check = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE noun = '{$noun}' LIMIT 1");
        if ($noun_check) {
            echo "  1. 存在noun='{$noun}'的项目，但docking不匹配\n";
            echo "     项目docking: {$noun_check['docking']}, 期望docking: {$hid}\n";
        } else {
            echo "  1. 不存在noun='{$noun}'的项目\n";
        }
    }
    
    // 检查docking字段匹配
    $docking_check = $DB->get_row("SELECT COUNT(*) as count FROM qingka_wangke_class WHERE docking = '{$hid}'");
    echo "  2. docking='{$hid}'的项目数量: {$docking_check['count']}\n";
}

echo "\n正确的查询逻辑应该是:\n";
echo "  SELECT * FROM qingka_wangke_class WHERE cid = '{$cid}'\n";

$correct_query_result = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = '{$cid}' LIMIT 1");
if ($correct_query_result) {
    echo "  ✅ 正确查询能找到项目\n";
    echo "  项目noun: '{$correct_query_result['noun']}'\n";
} else {
    echo "  ❌ 正确查询也找不到项目\n";
}

// 解决方案
echo "\n=== 解决方案 ===\n";
echo "问题: addWk函数中使用错误的查询条件\n";
echo "原因: 使用订单的noun字段去查询商品表，但两者可能不匹配\n";
echo "解决: 应该使用cid字段查询，因为cid是确定的商品ID\n";

echo "\n修复建议:\n";
echo "将 xdjk.php 第371行的查询改为:\n";
echo "  \$class_info = \$DB->get_row(\"SELECT * FROM qingka_wangke_class WHERE cid = '{\$cid}' LIMIT 1\");\n";

echo "\n或者确保订单表的noun字段与商品表的noun字段一致\n";

// 测试修复后的逻辑
echo "\n=== 测试修复后的逻辑 ===\n";
if ($correct_query_result) {
    $websiteNumber = $correct_query_result['noun'];
    echo "使用商品表的noun字段: '{$websiteNumber}'\n";
    echo "✅ 这样就能正确获取项目编号了\n";
} else {
    echo "❌ 商品信息本身就有问题\n";
}

echo "\n</pre>\n";
?>
