# 易教育手动配置指南

如果SQL脚本执行遇到问题，可以按照以下步骤手动配置易教育对接。

## 第一步：后台添加分类

1. 登录系统后台
2. 进入 **商品管理** → **分类设置**
3. 点击 **添加** 按钮
4. 填写以下信息：
   - **分类名称**: `易教育` （必须准确）
   - **分类描述**: `易教育平台聚合多个教育网站，支持查课和下单功能`
   - **排序**: `100`
   - **状态**: `启用`
5. 保存设置

## 第二步：后台添加货源

1. 进入 **网站管理** → **对接设置**
2. 点击 **添加** 按钮
3. 填写以下信息：
   - **货源名称**: `易教育`
   - **平台类型**: `jxjy`
   - **API地址**: `http://**************:9900`
   - **账号**: `573749877`
   - **密码**: `liuyaxin123.`
   - **IP地址**: `**************`
   - **状态**: `启用`
4. 保存设置

## 第三步：手动创建数据库表

### 3.1 创建易教育项目表

在phpMyAdmin或数据库管理工具中执行：

```sql
CREATE TABLE IF NOT EXISTS `qingka_wangke_jxjyclass` (
  `id` int(11) NOT NULL,
  `number` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `format` varchar(255) NOT NULL,
  `url` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `unitId` varchar(255) NOT NULL,
  `must` varchar(255) NOT NULL,
  `unit` varchar(255) NOT NULL,
  `rate` varchar(255) NOT NULL,
  `isExam` varchar(255) NOT NULL,
  `isSearchCourse` varchar(255) NOT NULL,
  `status` int(11) NOT NULL DEFAULT 1,
  `description` varchar(255) NOT NULL,
  `remark` varchar(255) NOT NULL,
  `date` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 3.2 创建易教育订单表

```sql
CREATE TABLE IF NOT EXISTS `qingka_wangke_jxjyorder` (
  `oid` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL,
  `cid` int(11) NOT NULL,
  `yid` varchar(255) NOT NULL DEFAULT '',
  `ptname` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `user` varchar(255) NOT NULL,
  `pass` varchar(255) NOT NULL,
  `kcid` text NOT NULL,
  `kcname` varchar(255) NOT NULL DEFAULT '',
  `fees` decimal(10,2) NOT NULL DEFAULT 0.00,
  `noun` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL DEFAULT '待更新',
  `process` varchar(255) NOT NULL DEFAULT '',
  `bsnum` varchar(255) NOT NULL DEFAULT '0',
  `remarks` varchar(255) NOT NULL DEFAULT '',
  `ip` varchar(255) NOT NULL,
  `addtime` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatetime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`oid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

### 3.3 添加索引（可选，提升性能）

```sql
-- 为项目表添加索引
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_number` (`number`);
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_status` (`status`);
ALTER TABLE `qingka_wangke_jxjyclass` ADD INDEX `idx_must` (`must`);

-- 为订单表添加索引
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_uid` (`uid`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_yid` (`yid`);
ALTER TABLE `qingka_wangke_jxjyorder` ADD INDEX `idx_status` (`status`);
```

## 第四步：验证配置

### 4.1 检查货源配置

执行以下SQL查询，确认货源已添加：

```sql
SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy';
```

应该返回一条记录，包含易教育的配置信息。

### 4.2 检查分类配置

```sql
SELECT * FROM qingka_wangke_fenlei WHERE name = '易教育';
```

应该返回一条记录，包含易教育分类信息。

### 4.3 检查表结构

```sql
SHOW TABLES LIKE '%jxjy%';
```

应该显示两个表：
- `qingka_wangke_jxjyclass`
- `qingka_wangke_jxjyorder`

## 第五步：同步商品数据

配置完成后，访问以下地址同步易教育商品：

```
http://你的域名/api/jxjy.php?pricee=5
```

如果看到类似以下输出，说明同步成功：

```
开始获取易教育项目列表...
找到 XX 个易教育项目，开始同步...
新增项目：国培网-融学Web - 售价：1.50元
...
=== 易教育同步完成 ===
处理项目总数: XX
成功上架: XX 条
```

## 第六步：测试功能

运行测试脚本验证功能：

```
http://你的域名/test/jxjy_test.php
```

## 常见问题解决

### 问题1：表已存在错误

如果提示表已存在，可以先删除再创建：

```sql
DROP TABLE IF EXISTS `qingka_wangke_jxjyclass`;
DROP TABLE IF EXISTS `qingka_wangke_jxjyorder`;
```

然后重新执行创建表的SQL。

### 问题2：字符集问题

如果遇到字符集问题，可以修改表的字符集：

```sql
ALTER TABLE `qingka_wangke_jxjyclass` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `qingka_wangke_jxjyorder` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 问题3：权限问题

确保数据库用户有以下权限：
- CREATE（创建表）
- ALTER（修改表结构）
- INSERT（插入数据）
- SELECT（查询数据）
- UPDATE（更新数据）

### 问题4：API连接失败

检查以下几点：
1. 服务器能否访问外网
2. 防火墙是否阻止了连接
3. API地址是否正确
4. 账号密码是否正确

## 完成确认

配置完成后，您应该能够：

1. ✅ 在后台看到易教育分类和货源
2. ✅ 数据库中有两个易教育相关的表
3. ✅ 商品同步脚本能正常运行
4. ✅ 测试脚本显示所有功能正常

如果以上都正常，说明易教育对接配置成功！
