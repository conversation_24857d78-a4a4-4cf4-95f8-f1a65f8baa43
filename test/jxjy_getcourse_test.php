<?php
/**
 * 易教育查课功能直接测试
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>易教育查课功能测试</h1>\n";
echo "<pre>\n";

// 获取易教育货源配置
$jk = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE pt = 'jxjy' OR instr(name,'易教育') LIMIT 1");
if (!$jk) {
    echo "❌ 未找到易教育货源配置\n";
    exit;
}

echo "✅ 易教育货源配置: ID={$jk['hid']}, 名称={$jk['name']}\n";

// 获取一个测试项目
$test_project = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE status=1 AND docking='{$jk['hid']}' LIMIT 1");
if (!$test_project) {
    echo "❌ 没有找到易教育测试项目\n";
    exit;
}

echo "✅ 找到测试项目: ID={$test_project['cid']}, 名称={$test_project['name']}, noun={$test_project['noun']}\n";

// 测试1：模拟前端获取项目列表
echo "\n=== 测试1：模拟前端获取项目列表 ===\n";
$projects = $DB->query("SELECT * FROM qingka_wangke_class WHERE status=1 AND docking='{$jk['hid']}' ORDER BY cid ASC LIMIT 5");
$project_list = array();
while ($row = $DB->fetch($projects)) {
    $project_list[] = $row;
    echo "项目: ID={$row['cid']}, 名称={$row['name']}, noun={$row['noun']}\n";
}

// 测试2：模拟查课API调用
echo "\n=== 测试2：模拟查课API调用 ===\n";
$test_id = $test_project['cid'];
$test_user = 'test_user';
$test_pass = 'test_pass';

echo "使用参数: id={$test_id}, user={$test_user}, pass={$test_pass}\n";

// 模拟查课逻辑
$rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='$test_id' LIMIT 1");

if (!$rs) {
    echo "❌ 项目信息不存在，项目ID: {$test_id}\n";
} else {
    echo "✅ 找到项目信息\n";
    echo "   项目ID: {$rs['cid']}\n";
    echo "   项目名称: {$rs['name']}\n";
    echo "   对接货源: {$rs['docking']}\n";
    echo "   项目编号(noun): {$rs['noun']}\n";
    
    // 检查是否为易教育项目
    if ($rs['docking'] != $jk['hid']) {
        echo "❌ 该项目不是易教育项目，无法查课\n";
        echo "   项目对接货源: {$rs['docking']}\n";
        echo "   易教育货源ID: {$jk['hid']}\n";
    } else {
        echo "✅ 项目验证通过，是易教育项目\n";
        
        $number = $rs['noun'];
        echo "✅ 项目编号: {$number}\n";
        
        // 模拟API调用（不实际发送请求）
        echo "✅ 查课API参数验证通过\n";
        echo "   websiteNumber: {$number}\n";
        echo "   username: {$test_user}\n";
        echo "   password: {$test_pass}\n";
    }
}

// 测试3：检查API文件
echo "\n=== 测试3：检查API文件 ===\n";
$api_file = dirname(__FILE__) . '/../api/jxjyapi.php';
if (file_exists($api_file)) {
    echo "✅ API文件存在: {$api_file}\n";
    
    // 检查文件内容
    $content = file_get_contents($api_file);
    if (strpos($content, 'qingka_wangke_class') !== false) {
        echo "✅ API文件已修复，使用 qingka_wangke_class 表\n";
    } else {
        echo "⚠️  API文件可能未正确修复\n";
    }
    
    if (strpos($content, 'function_exists(\'post\')') !== false) {
        echo "✅ post函数重复定义问题已修复\n";
    } else {
        echo "⚠️  post函数重复定义问题可能未修复\n";
    }
} else {
    echo "❌ API文件不存在\n";
}

// 测试4：直接调用API（通过HTTP请求）
echo "\n=== 测试4：直接调用API ===\n";
try {
    // 构建请求数据
    $post_data = array(
        'act' => 'getcourse',
        'id' => $test_id,
        'user' => $test_user,
        'pass' => $test_pass
    );
    
    // 获取当前域名
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $api_url = "{$protocol}://{$host}/api/jxjyapi.php";
    
    echo "调用API: {$api_url}\n";
    echo "参数: " . json_encode($post_data) . "\n";
    
    // 使用curl发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        echo "❌ CURL错误: {$curl_error}\n";
    } else {
        echo "✅ HTTP状态码: {$http_code}\n";
        echo "响应内容: {$response}\n";
        
        // 尝试解析JSON响应
        $result = json_decode($response, true);
        if ($result) {
            if (isset($result['code'])) {
                if ($result['code'] == 1) {
                    echo "✅ API调用成功\n";
                } else {
                    echo "❌ API返回错误: {$result['msg']}\n";
                }
            } else {
                echo "⚠️  响应格式异常\n";
            }
        } else {
            echo "⚠️  响应不是有效的JSON格式\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ API调用异常: " . $e->getMessage() . "\n";
}

// 测试总结
echo "\n=== 测试总结 ===\n";
echo "如果以上测试都通过，但前端仍然提示项目编号不存在，可能的原因:\n";
echo "1. 前端缓存问题 - 清除浏览器缓存重试\n";
echo "2. 前端调用的API路径不正确\n";
echo "3. 前端传递的参数格式有问题\n";
echo "4. 服务器配置问题（如URL重写规则）\n";
echo "\n建议:\n";
echo "1. 在浏览器开发者工具中查看网络请求\n";
echo "2. 检查实际发送的API请求和响应\n";
echo "3. 确认前端调用的API路径是否正确\n";

echo "\n</pre>\n";
?>
