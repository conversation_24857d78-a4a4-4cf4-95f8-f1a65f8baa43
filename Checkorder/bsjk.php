<?php  
function budanWk($oid){
	global $DB;
	global $wk;
	$d = $DB->get_row("select * from qingka_wangke_order where oid='{$oid}' ");
	$b = $DB->get_row("select hid,yid,user from qingka_wangke_order where oid='{$oid}' ");
	$hid = $b["hid"];
	$yid = $b["yid"];
	$user = $b["user"];
	$a = $DB->get_row("select * from qingka_wangke_huoyuan where hid='{$hid}' ");
	$type = $a["pt"];
	$cookie = $a["cookie"];
	$token = $a["token"];
	$ip = $a["ip"];
	$cid = $d["cid"];
	$school = $d["school"];
	$user = $d["user"];
	$pass = $d["pass"];
	$kcid = $d["kcid"];
	$kcname = $d["kcname"];
	$noun = $d["noun"];
	$miaoshua = $d["miaoshua"];

	//YYY补刷接口
	if ($type == "yyy") {

		$data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $noun, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "kcid" => $kcid, "yid" => $yid);
		$dx_rl = $a["url"];
		$dx_url = "$dx_rl/api/bushua";

		$result = get_url($dx_url, $data);
		$result = json_decode($result, true);

        if ($result["code"] == "200") {
			$b = array("code" => 1, "msg" => "补刷成功");
        } else {
			$b = array("code" => -1, "msg" => $result["message"]);
        }
		return $b;
	}

	//8090edu补刷接口
	if ($type == "8090edu") {
		// 检查Token是否存在
		if (empty($token)) {
			return array("code" => -1, "msg" => "8090教育Token未配置");
		}

		// 构建补刷请求数据
		$refresh_data = array(
			"orderId" => intval($yid),
			"status" => "队列中",
			"reason" => ""
		);

		// 8090教育补刷API地址
		$refresh_url = $a["url"] . "/api/order/status/update";

		// 设置请求头
		$headers = array(
			"Authorization: Bearer " . $token,
			"Content-Type: application/json",
			"Accept: application/json"
		);

		// 发送补刷请求
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $refresh_url);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($refresh_data));
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 30);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

		$response = curl_exec($ch);
		$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		$curl_error = curl_error($ch);
		curl_close($ch);

		// 处理请求错误
		if ($curl_error) {
			return array("code" => -1, "msg" => "网络请求失败: " . $curl_error);
		}

		// 处理HTTP错误
		if ($http_code !== 200) {
			return array("code" => -1, "msg" => "HTTP请求失败，状态码: " . $http_code);
		}

		// 解析响应
		$result = json_decode($response, true);

		if (!$result) {
			return array("code" => -1, "msg" => "响应解析失败");
		}

		// 检查API响应
		if (isset($result["code"]) && $result["code"] == 200 && isset($result["state"]) && $result["state"] === true) {
			return array("code" => 1, "msg" => "8090教育补刷成功，订单已重新进入队列");
		} else {
			$error_msg = isset($result["message"]) ? $result["message"] : "未知错误";
			return array("code" => -1, "msg" => "8090教育补刷失败: " . $error_msg);
		}
	}

    if ($type == "29") {
        $data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
        $ace_rl = $a["url"];
        $ace_url = "$ace_rl/api.php?act=budan";
        $result = get_url($ace_url, $data);
        $result = json_decode($result, true);
        return $result;
    }
    
    //暗网补刷
    if ($type == "bdkj") {
       $data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
       $dx_rl = $a["url"];
       $dx_url = "$dx_rl/api.php?act=budan";
       $result = get_url($dx_url, $data);
       $result = json_decode($result, true);
       return $result;
    }

    //易教育补刷接口
    if ($type == "jxjy") {
        // 易教育平台通过重新下单实现补刷功能
        // 智能Token管理 - 优先使用缓存的token，失效时自动刷新
        $token = $a["token"];
        $need_refresh_token = false;

        // 检查是否有缓存的token
        if (empty($token)) {
            $need_refresh_token = true;
        } else {
            // 验证token是否有效
            $test_url = "{$a["url"]}/api/user/info";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $test_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Authorization: Bearer {$token}"));
            $test_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$test_result) {
                $need_refresh_token = true;
            } else {
                $test_result_array = json_decode($test_result, true);
                if (!$test_result_array || !isset($test_result_array["code"]) || $test_result_array["code"] != 200) {
                    $need_refresh_token = true;
                }
            }
        }

        // 如果需要刷新token，重新登录
        if ($need_refresh_token) {
            $login_data = array(
                "username" => $a["user"],
                "password" => $a["pass"]
            );

            $login_url = "{$a["url"]}/api/login";

            // 使用curl发送JSON请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $login_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($login_data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
            $login_result = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error || !$login_result) {
                return array("code" => -1, "msg" => "登录失败：网络错误");
            }

            $login_result_array = json_decode($login_result, true);

            if (!$login_result_array || !isset($login_result_array["code"]) || $login_result_array["code"] != 200) {
                $error_msg = isset($login_result_array["message"]) ? $login_result_array["message"] : "登录失败";
                return array("code" => -1, "msg" => $error_msg);
            }

            $token = $login_result_array["data"]["token"];

            // 更新数据库中的token
            $DB->query("UPDATE qingka_wangke_huoyuan SET token = '{$token}' WHERE hid = '{$hid}'");
        }

        // 获取项目信息 - 使用项目编号查询
        $class_info = $DB->get_row("SELECT * FROM qingka_wangke_jxjyclass WHERE number = '{$noun}' LIMIT 1");
        if (!$class_info) {
            return array("code" => -1, "msg" => "项目信息不存在，项目编号: {$noun}");
        }

        $websiteNumber = $class_info['number'];
        $isSearchCourse = $class_info['isSearchCourse'];

        // 构建重新下单数据（补刷通过重新下单实现）
        if ($isSearchCourse == '0') {
            // 无需查课的项目，直接下单
            $order_data = array(
                "websiteNumber" => $websiteNumber,
                "data" => array(array(
                    "username" => $user,
                    "password" => $pass
                ))
            );
        } else {
            // 需要查课的项目，包含课程信息
            if (strpos($kcname, '----') !== false) {
                // 处理嵌套课程结构
                $course_parts = explode('----', $kcname);
                $main_course = $course_parts[0];
                $sub_course = $course_parts[1];

                $order_data = array(
                    "websiteNumber" => $websiteNumber,
                    "data" => array(array(
                        "username" => $user,
                        "password" => $pass,
                        "name" => $user . "----" . $pass,
                        "children" => array(array(
                            "name" => $main_course,
                            "children" => array(array(
                                "name" => $sub_course,
                                "disabled" => false,
                                "id" => $kcid,
                                "selected" => true
                            )),
                            "disabled" => true,
                            "selected" => true
                        )),
                        "selected" => true
                    ))
                );
            } else {
                // 普通课程结构
                $order_data = array(
                    "websiteNumber" => $websiteNumber,
                    "data" => array(array(
                        "username" => $user,
                        "password" => $pass,
                        "name" => $user . "----" . $pass,
                        "children" => array(array(
                            "name" => $kcname,
                            "disabled" => false,
                            "id" => $kcid,
                            "selected" => true
                        )),
                        "selected" => true
                    ))
                );
            }
        }

        // 发送重新下单请求
        $order_url = "{$a["url"]}/api/order/buy";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $order_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer {$token}"
        ));
        $order_result = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error || !$order_result) {
            return array("code" => -1, "msg" => "补刷失败：网络错误");
        }

        $order_result_array = json_decode($order_result, true);

        if (!$order_result_array || !isset($order_result_array["code"]) || $order_result_array["code"] != 200) {
            $error_msg = isset($order_result_array["message"]) ? $order_result_array["message"] : "补刷失败";
            return array("code" => -1, "msg" => $error_msg);
        }

        // 获取新的订单ID并更新数据库
        $new_yid = "";
        if (isset($order_result_array["data"]["orderList"]) && is_array($order_result_array["data"]["orderList"])) {
            foreach ($order_result_array["data"]["orderList"] as $order) {
                if (isset($order["orderId"]) && $order["username"] == $user) {
                    $new_yid = $order["orderId"];
                    break;
                }
            }
        }

        if (!empty($new_yid)) {
            // 更新订单的yid
            $DB->query("UPDATE qingka_wangke_order SET yid = '{$new_yid}' WHERE oid = '{$oid}'");
        }

        return array("code" => 1, "msg" => "易教育补刷成功，已重新提交订单");
    }

	else {
				$b = array("code" => -1, "msg" => "接口异常，请联系管理员");
		}
}




?>