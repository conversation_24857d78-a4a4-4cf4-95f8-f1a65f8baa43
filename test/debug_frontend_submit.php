<?php
/**
 * 调试前端提交问题
 * 模拟前端提交订单的完整过程
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>调试前端提交问题</h1>\n";
echo "<pre>\n";

// 模拟用户登录状态
$userrow = array(
    'uid' => 1,
    'addprice' => 1,
    'money' => 1000
);

// 模拟前端提交的数据
$_POST = array(
    'cid' => '1', // 易教育项目ID
    'data' => json_encode(array(
        array(
            'userinfo' => 'test_user test_pass',
            'userName' => '测试用户',
            'data' => array(
                'id' => 'test_course_' . time(),
                'name' => '测试课程_' . time(),
                'kcjs' => '2025-12-31'
            )
        )
    ))
);

echo "模拟POST数据:\n";
echo "cid: {$_POST['cid']}\n";
echo "data: {$_POST['data']}\n";

// 开始模拟apisub.php的add逻辑
$cid = trim(strip_tags($_POST["cid"]));
$data = json_decode($_POST["data"], true);

echo "\n解析后的数据:\n";
echo "cid: {$cid}\n";
echo "data: " . print_r($data, true) . "\n";

// 获取项目信息
$rs = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='$cid' LIMIT 1");
if (!$rs) {
    echo "❌ 项目不存在\n";
    exit;
}

echo "项目信息:\n";
echo "  name: {$rs['name']}\n";
echo "  price: {$rs['price']}\n";
echo "  docking: {$rs['docking']}\n";

// 计算价格
if ($rs['yunsuan'] == "*") {
    $danjia = round($rs['price'] * $userrow['addprice'], 2);
} elseif ($rs['yunsuan'] == "+") {
    $danjia = round($rs['price'] + $userrow['addprice'], 2);
} else {
    $danjia = round($rs['price'] * $userrow['addprice'], 2);
}

echo "\n价格计算:\n";
echo "  原价: {$rs['price']}\n";
echo "  用户加成: {$userrow['addprice']}\n";
echo "  单价: {$danjia}\n";

// 检查密价
$mijia = $DB->get_row("SELECT * FROM qingka_wangke_mijia WHERE uid='{$userrow['uid']}' AND cid='$cid'");
if ($mijia) {
    echo "  发现密价配置\n";
    if ($mijia['mode'] == 0) {
        $mijia_price = round($danjia - $mijia['price'], 2);
    } elseif ($mijia['mode'] == 1) {
        $mijia_price = round(($rs['price'] - $mijia['price']) * $userrow['addprice'], 2);
    } elseif ($mijia['mode'] == 2) {
        $mijia_price = $mijia['price'];
    }
    if ($mijia_price <= 0) {
        $mijia_price = 0;
    }
    $danjia = $mijia_price;
    echo "  密价: {$danjia}\n";
}

// 计算总费用
$money = count($data) * $danjia;
echo "\n费用计算:\n";
echo "  订单数量: " . count($data) . "\n";
echo "  总费用: {$money}\n";
echo "  用户余额: {$userrow['money']}\n";

if ($userrow['money'] < $money) {
    echo "❌ 余额不足\n";
    exit;
}

// 处理每个订单
$clientip = '127.0.0.1';
$date = date('Y-m-d H:i:s');
$miaoshua = '0';
$success_count = 0;
$error_count = 0;

foreach ($data as $row) {
    echo "\n处理订单 " . ($success_count + $error_count + 1) . ":\n";
    
    $userinfo = $row['userinfo'];
    $userName = $row['userName'];
    $userinfo = explode(" ", $userinfo);
    
    if (count($userinfo) > 2) {
        $school = $userinfo[0];
        $user = $userinfo[1];
        $pass = $userinfo[2];
    } else {
        $school = "自动识别";
        $user = $userinfo[0];
        $pass = $userinfo[1];
    }
    
    $kcid = $row['data']['id'];
    $kcname = $row['data']['name'];
    $kcjs = $row['data']['kcjs'];
    
    echo "  用户信息: {$school} / {$user} / {$pass}\n";
    echo "  课程信息: {$kcid} / {$kcname} / {$kcjs}\n";
    
    // 检查重复订单
    $duplicate = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE ptname='{$rs['name']}' AND school='$school' AND user='$user' AND pass='$pass' AND kcid='$kcid' AND kcname='$kcname'");
    
    if ($duplicate) {
        $dockstatus = '3';
        echo "  ⚠️  检测到重复订单，dockstatus=3\n";
    } elseif ($rs['docking'] == 0) {
        $dockstatus = '99';
        echo "  ⚠️  项目未对接，dockstatus=99\n";
    } else {
        $dockstatus = '0';
        echo "  ✅ 正常订单，dockstatus=0\n";
    }
    
    // 构建SQL
    $sql = "INSERT INTO qingka_wangke_order (uid, cid, hid, ptname, school, name, user, pass, kcid, kcname, courseEndTime, fees, noun, miaoshua, addtime, ip, dockstatus) VALUES ('{$userrow['uid']}','{$rs['cid']}','{$rs['docking']}','{$rs['name']}','{$school}','$userName','$user','$pass','$kcid','$kcname','{$kcjs}','{$danjia}','{$rs['noun']}','$miaoshua','$date','$clientip','$dockstatus')";
    
    echo "  SQL: " . substr($sql, 0, 100) . "...\n";
    
    // 执行插入
    $is = $DB->query($sql);
    
    if ($is) {
        $success_count++;
        echo "  ✅ 订单插入成功\n";
        
        // 获取订单ID
        $oid = $DB->lastInsertId();
        echo "  订单ID: {$oid}\n";
        
    } else {
        $error_count++;
        $error = $DB->error();
        echo "  ❌ 订单插入失败: {$error}\n";
        
        // 检查具体的错误原因
        if (strpos($error, 'Duplicate entry') !== false) {
            echo "  原因: 主键或唯一键冲突\n";
        } elseif (strpos($error, "doesn't have a default value") !== false) {
            echo "  原因: 某个必填字段没有提供值\n";
        } elseif (strpos($error, 'Data too long') !== false) {
            echo "  原因: 数据长度超出字段限制\n";
        }
    }
}

echo "\n=== 处理结果 ===\n";
echo "成功: {$success_count}\n";
echo "失败: {$error_count}\n";

if ($success_count > 0) {
    echo "\n模拟扣费...\n";
    $total_cost = $success_count * $danjia;
    echo "扣费金额: {$total_cost}\n";
    
    // 这里不实际扣费，只是模拟
    echo "✅ 模拟扣费成功\n";
    
    echo "\n最终结果: 提交成功\n";
} else {
    echo "\n最终结果: 提交失败\n";
}

echo "\n</pre>\n";
?>
