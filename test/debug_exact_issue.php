<?php
/**
 * 精确定位问题
 */

// 引入公共配置文件
include_once(dirname(__FILE__) . '/../confing/common.php');
include_once(dirname(__FILE__) . '/../Checkorder/xdjk.php');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>精确定位问题</h1>\n";
echo "<pre>\n";

// 获取一个具体的失败订单
$failed_oid = '2147483682';
echo "=== 分析具体失败订单: {$failed_oid} ===\n";

$order = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE oid='{$failed_oid}' LIMIT 1");

if (!$order) {
    // 如果订单不存在，找最近的一个
    echo "订单不存在，查找最近的失败订单...\n";
    $recent = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE hid=50 AND dockstatus IN (0,5) ORDER BY oid DESC LIMIT 1");
    if ($recent) {
        $order = $recent;
        $failed_oid = $order['oid'];
        echo "使用订单: {$failed_oid}\n";
    } else {
        echo "❌ 没有找到可分析的订单\n";
        exit;
    }
}

echo "\n订单详细信息:\n";
foreach ($order as $key => $value) {
    echo "  {$key}: '{$value}'\n";
}

// 直接调用addWk函数看具体错误
echo "\n=== 直接调用addWk函数 ===\n";
echo "调用 addWk({$failed_oid})\n";

try {
    $result = addWk($failed_oid);
    
    echo "\n返回结果:\n";
    echo "  code: " . (isset($result['code']) ? $result['code'] : 'undefined') . "\n";
    echo "  msg: " . (isset($result['msg']) ? $result['msg'] : 'undefined') . "\n";
    
    if (isset($result['code']) && $result['code'] == -1) {
        echo "\n❌ 调用失败，错误信息: {$result['msg']}\n";
        
        // 分析错误信息
        if (strpos($result['msg'], '项目信息不存在') !== false) {
            echo "\n问题确认：项目信息查询失败\n";
            
            // 手动执行查询看看具体问题
            echo "\n手动执行查询:\n";
            $cid = $order['cid'];
            echo "  订单cid: {$cid}\n";
            
            $manual_query = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = '{$cid}' LIMIT 1");
            if ($manual_query) {
                echo "  ✅ 手动查询成功，项目存在\n";
                echo "  项目名称: {$manual_query['name']}\n";
                echo "  项目docking: {$manual_query['docking']}\n";
                echo "  项目noun: '{$manual_query['noun']}'\n";
                
                // 检查货源验证
                $expected_hid = $order['hid'];
                if ($manual_query['docking'] == $expected_hid) {
                    echo "  ✅ 货源验证通过\n";
                    echo "  这说明修复的逻辑是正确的，但可能有其他问题\n";
                } else {
                    echo "  ❌ 货源验证失败\n";
                    echo "  期望货源: {$expected_hid}\n";
                    echo "  实际货源: {$manual_query['docking']}\n";
                    echo "  这就是问题所在！\n";
                }
            } else {
                echo "  ❌ 手动查询失败，项目不存在\n";
                echo "  订单中的cid无效: {$cid}\n";
            }
        }
    } else {
        echo "✅ 调用成功\n";
    }
    
} catch (Exception $e) {
    echo "❌ 调用异常: " . $e->getMessage() . "\n";
}

// 检查addWk函数的具体执行路径
echo "\n=== 检查addWk函数执行路径 ===\n";

// 模拟addWk函数的开始部分
$d = $DB->get_row("SELECT * FROM qingka_wangke_order WHERE oid='{$failed_oid}' LIMIT 1");
$cid = $d["cid"];
$school = $d["school"];
$user = $d["user"];
$pass = $d["pass"];
$kcid = $d["kcid"];
$kcname = $d["kcname"];
$noun = $d["noun"];
$miaoshua = $d["miaoshua"];

echo "从订单获取的变量:\n";
echo "  cid: {$cid}\n";
echo "  noun: '{$noun}'\n";
echo "  user: {$user}\n";
echo "  kcname: {$kcname}\n";

$b = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid='{$cid}' LIMIT 1");
if ($b) {
    echo "\n✅ 商品查询成功\n";
    $hid = $b["docking"];
    echo "  hid: {$hid}\n";
    
    $a = $DB->get_row("SELECT * FROM qingka_wangke_huoyuan WHERE hid='{$hid}' LIMIT 1");
    if ($a) {
        echo "\n✅ 货源查询成功\n";
        $type = $a["pt"];
        echo "  type: {$type}\n";
        
        if ($type == "jxjy") {
            echo "\n✅ 确认为易教育订单\n";
            
            // 这里是修复后的逻辑
            echo "\n执行修复后的项目查询:\n";
            echo "  SQL: SELECT * FROM qingka_wangke_class WHERE cid = '{$cid}'\n";
            $class_info = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = '{$cid}' LIMIT 1");
            
            if ($class_info) {
                echo "  ✅ 项目查询成功\n";
                
                // 验证货源
                if ($class_info['docking'] == $hid) {
                    echo "  ✅ 货源验证通过\n";
                    echo "  这个订单应该能正常处理！\n";
                    
                    // 检查为什么还是失败
                    echo "\n如果还是失败，可能的原因:\n";
                    echo "  1. 代码修改没有生效\n";
                    echo "  2. 缓存问题\n";
                    echo "  3. 其他地方的错误\n";
                    
                } else {
                    echo "  ❌ 货源验证失败\n";
                    echo "  期望: {$hid}\n";
                    echo "  实际: {$class_info['docking']}\n";
                    echo "  这就是问题！订单的hid与商品的docking不匹配\n";
                }
            } else {
                echo "  ❌ 项目查询失败\n";
                echo "  cid无效: {$cid}\n";
            }
        } else {
            echo "\n❌ 不是易教育订单，type: {$type}\n";
        }
    } else {
        echo "\n❌ 货源查询失败，hid: {$hid}\n";
    }
} else {
    echo "\n❌ 商品查询失败，cid: {$cid}\n";
}

// 最终诊断
echo "\n=== 最终诊断 ===\n";

if ($b && $a && $type == "jxjy" && $class_info && $class_info['docking'] == $hid) {
    echo "✅ 所有数据都正常，修复应该生效\n";
    echo "\n如果还是失败，请检查:\n";
    echo "1. 确认修改的文件是否正确\n";
    echo "2. 重启相关进程\n";
    echo "3. 清除可能的缓存\n";
} else {
    echo "❌ 发现数据问题\n";
    
    if (!$b) {
        echo "  - 商品数据不存在\n";
    }
    if (!$a) {
        echo "  - 货源数据不存在\n";
    }
    if ($type != "jxjy") {
        echo "  - 不是易教育项目\n";
    }
    if (!$class_info) {
        echo "  - 项目查询失败\n";
    }
    if ($class_info && $class_info['docking'] != $hid) {
        echo "  - 货源不匹配\n";
    }
}

echo "\n</pre>\n";
?>
